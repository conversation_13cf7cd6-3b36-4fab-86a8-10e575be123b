import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/utils/unified_logger.dart';
import '../../../core/auth/unified_auth_provider.dart';
import '../../../core/networking/simple_api_client.dart';
import '../models/user_vehicle.dart';
import '../repositories/garage_repository.dart';

part 'garage_provider.g.dart';

// Provider for the garage repository
@riverpod
GarageRepository garageRepository(Ref ref) {
  final apiClient = ref.watch(simpleApiClientProvider);
  return GarageRepository(apiClient);
}

@riverpod
class GarageData extends _$GarageData {
  @override
  Future<List<UserVehicle>> build() async {
    // Use the correct currentUser provider from auth_providers
    final currentUser = ref.watch(currentUserProvider);

    if (currentUser == null) {
      UnifiedLogger.warning('User not authenticated, returning empty garage');
      return [];
    }

    try {
      UnifiedLogger.info('Loading garage data for user: ${currentUser.id}');
      final repository = ref.watch(garageRepositoryProvider);
      final vehicles = await repository.getUserVehicles(currentUser.id);

      UnifiedLogger.info('Successfully loaded ${vehicles.length} vehicles');
      return vehicles;
    } catch (error, stackTrace) {
      UnifiedLogger.error('Failed to load garage data', error: error, stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<void> addVehicle(UserVehicle vehicle) async {
    final currentUser = ref.read(currentUserProvider);
    
    if (currentUser == null) {
      UnifiedLogger.error('Cannot add vehicle: user not authenticated');
      throw Exception('User not authenticated');
    }

    try {
      UnifiedLogger.info('Adding vehicle: ${vehicle.make} ${vehicle.model}');
      final repository = ref.read(garageRepositoryProvider);
      
      final vehicleWithUser = vehicle.copyWith(userId: currentUser.id);
      await repository.addVehicle(vehicleWithUser);
      
      UnifiedLogger.info('Vehicle added successfully');
      
      // Refresh the provider
      ref.invalidateSelf();
    } catch (error, stackTrace) {
      UnifiedLogger.error('Failed to add vehicle', error: error, stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<void> updateVehicle(UserVehicle vehicle) async {
    try {
      UnifiedLogger.info('Updating vehicle: ${vehicle.id}');
      final repository = ref.read(garageRepositoryProvider);
      await repository.updateVehicle(vehicle);
      
      UnifiedLogger.info('Vehicle updated successfully');
      
      // Refresh the provider
      ref.invalidateSelf();
    } catch (error, stackTrace) {
      UnifiedLogger.error('Failed to update vehicle', error: error, stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<void> removeVehicle(String vehicleId) async {
    try {
      UnifiedLogger.info('Removing vehicle: $vehicleId');
      final repository = ref.read(garageRepositoryProvider);
      await repository.removeVehicle(vehicleId);
      
      UnifiedLogger.info('Vehicle removed successfully');
      
      // Refresh the provider
      ref.invalidateSelf();
    } catch (error, stackTrace) {
      UnifiedLogger.error('Failed to remove vehicle', error: error, stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<void> setDefaultVehicle(String vehicleId) async {
    final currentUser = ref.read(currentUserProvider);
    
    if (currentUser == null) {
      UnifiedLogger.error('Cannot set default vehicle: user not authenticated');
      return;
    }

    try {
      UnifiedLogger.info('Setting default vehicle: $vehicleId');
      final repository = ref.read(garageRepositoryProvider);
      await repository.setDefaultVehicle(currentUser.id, vehicleId);
      
      UnifiedLogger.info('Default vehicle set successfully');
      
      // Refresh the provider
      ref.invalidateSelf();
    } catch (error, stackTrace) {
      UnifiedLogger.error('Failed to set default vehicle', error: error, stackTrace: stackTrace);
      rethrow;
    }
  }

  void refresh() {
    UnifiedLogger.info('Refreshing garage data');
    ref.invalidateSelf();
  }
}
