// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$startupPerformanceTrackerHash() =>
    r'65dc0a74bbc4f5db73379fe4d384d9779b6c755c';

/// Startup performance tracker - simplified
///
/// Copied from [StartupPerformanceTracker].
@ProviderFor(StartupPerformanceTracker)
final startupPerformanceTrackerProvider =
    AutoDisposeNotifierProvider<
      StartupPerformanceTracker,
      Map<String, dynamic>
    >.internal(
      StartupPerformanceTracker.new,
      name: r'startupPerformanceTrackerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$startupPerformanceTrackerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$StartupPerformanceTracker = AutoDisposeNotifier<Map<String, dynamic>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
