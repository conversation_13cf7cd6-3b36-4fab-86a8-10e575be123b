// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$dataIntegrityServiceHash() =>
    r'45ed389bc18564bf41054e8de5d414c83f64adaf';

/// See also [dataIntegrityService].
@ProviderFor(dataIntegrityService)
final dataIntegrityServiceProvider = Provider<DataIntegrityService>.internal(
  dataIntegrityService,
  name: r'dataIntegrityServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$dataIntegrityServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DataIntegrityServiceRef = ProviderRef<DataIntegrityService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
