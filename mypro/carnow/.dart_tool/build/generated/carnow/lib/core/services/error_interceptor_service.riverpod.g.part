// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$errorInterceptorServiceHash() =>
    r'1fb580316256643ede5b8aed982f42e90e299e68';

/// Provider لخدمة Error Interceptor
///
/// Copied from [errorInterceptorService].
@ProviderFor(errorInterceptorService)
final errorInterceptorServiceProvider =
    AutoDisposeProvider<ErrorInterceptorService>.internal(
      errorInterceptorService,
      name: r'errorInterceptorServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$errorInterceptorServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ErrorInterceptorServiceRef =
    AutoDisposeProviderRef<ErrorInterceptorService>;
String _$dioWithErrorHandlingHash() =>
    r'92bf3408ca58f3c0489994c2ba0a436db3c39d56';

/// Provider لـ Dio مع Error Handling
///
/// Copied from [dioWithErrorHandling].
@ProviderFor(dioWithErrorHandling)
final dioWithErrorHandlingProvider = AutoDisposeProvider<Dio>.internal(
  dioWithErrorHandling,
  name: r'dioWithErrorHandlingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$dioWithErrorHandlingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DioWithErrorHandlingRef = AutoDisposeProviderRef<Dio>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
