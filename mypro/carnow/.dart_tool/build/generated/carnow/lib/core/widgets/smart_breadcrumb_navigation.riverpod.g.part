// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentBreadcrumbHash() => r'7b0723f660a8c59407f89defc1e35f9acf1348c8';

/// مزود التنقل التدريجي الحالي
///
/// Copied from [CurrentBreadcrumb].
@ProviderFor(CurrentBreadcrumb)
final currentBreadcrumbProvider =
    AutoDisposeNotifierProvider<
      CurrentBreadcrumb,
      List<BreadcrumbItem>
    >.internal(
      CurrentBreadcrumb.new,
      name: r'currentBreadcrumbProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$currentBreadcrumbHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CurrentBreadcrumb = AutoDisposeNotifier<List<BreadcrumbItem>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
