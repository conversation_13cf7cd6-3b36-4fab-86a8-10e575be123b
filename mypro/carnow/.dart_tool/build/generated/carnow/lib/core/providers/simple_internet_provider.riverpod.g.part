// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$internetConnectionHash() =>
    r'9a67926dc4dde29aecba286fbe96e3dbf6d73efa';

/// Internet connectivity status - simplified
///
/// Copied from [internetConnection].
@ProviderFor(internetConnection)
final internetConnectionProvider = AutoDisposeStreamProvider<bool>.internal(
  internetConnection,
  name: r'internetConnectionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$internetConnectionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef InternetConnectionRef = AutoDisposeStreamProviderRef<bool>;
String _$internetStatusHash() => r'7f6f4aedc4f67fcedaf5d84cb4a942419fc8465f';

/// Current internet status - simplified
///
/// Copied from [internetStatus].
@ProviderFor(internetStatus)
final internetStatusProvider = AutoDisposeFutureProvider<bool>.internal(
  internetStatus,
  name: r'internetStatusProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$internetStatusHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef InternetStatusRef = AutoDisposeFutureProviderRef<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
