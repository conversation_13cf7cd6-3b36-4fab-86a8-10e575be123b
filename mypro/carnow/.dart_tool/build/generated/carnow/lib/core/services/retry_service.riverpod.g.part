// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$retryServiceHash() => r'f6ecbda88f1ce3846cdb9be06032fcce24f3bfb9';

/// Retry Service for Phase 3.2.1: Retry and Timeout Logic
///
/// Features:
/// - Exponential backoff retry mechanism
/// - Configurable timeout values
/// - Dead letter queue for failed operations
/// - Graceful service degradation
///
/// Copied from [retryService].
@ProviderFor(retryService)
final retryServiceProvider = AutoDisposeProvider<RetryService>.internal(
  retryService,
  name: r'retryServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$retryServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef RetryServiceRef = AutoDisposeProviderRef<RetryService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
