// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$networkConnectivityServiceHash() =>
    r'bf942a23556a67b247a9c53b4abe7f08a5326457';

/// Provider for network connectivity service
///
/// Copied from [networkConnectivityService].
@ProviderFor(networkConnectivityService)
final networkConnectivityServiceProvider =
    AutoDisposeProvider<NetworkConnectivityService>.internal(
      networkConnectivityService,
      name: r'networkConnectivityServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$networkConnectivityServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef NetworkConnectivityServiceRef =
    AutoDisposeProviderRef<NetworkConnectivityService>;
String _$isBackendAvailableHash() =>
    r'b015292255fdf5cc1f8c9dca6e106a3f799816fc';

/// Provider for backend availability status
///
/// Copied from [isBackendAvailable].
@ProviderFor(isBackendAvailable)
final isBackendAvailableProvider = AutoDisposeProvider<bool>.internal(
  isBackendAvailable,
  name: r'isBackendAvailableProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isBackendAvailableHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsBackendAvailableRef = AutoDisposeProviderRef<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
