// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$deadLetterQueueServiceHash() =>
    r'1a8c11478d6829275e1d70265b13da71690aff7f';

/// Dead Letter Queue service to handle operations that failed after all retry attempts
/// Stores failed operations for later processing or analysis
///
/// Copied from [deadLetterQueueService].
@ProviderFor(deadLetterQueueService)
final deadLetterQueueServiceProvider =
    AutoDisposeProvider<DeadLetterQueueService>.internal(
      deadLetterQueueService,
      name: r'deadLetterQueueServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$deadLetterQueueServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DeadLetterQueueServiceRef =
    AutoDisposeProviderRef<DeadLetterQueueService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
