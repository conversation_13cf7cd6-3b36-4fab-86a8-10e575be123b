// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentServerHash() => r'6d34a6cf1b8ee8041e16d5d11cd00f8c04f2cbd5';

/// Provider لحالة السيرفر الحالي
///
/// Copied from [CurrentServer].
@ProviderFor(CurrentServer)
final currentServerProvider =
    AutoDisposeNotifierProvider<CurrentServer, ServerInfo?>.internal(
      CurrentServer.new,
      name: r'currentServerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$currentServerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CurrentServer = AutoDisposeNotifier<ServerInfo?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
