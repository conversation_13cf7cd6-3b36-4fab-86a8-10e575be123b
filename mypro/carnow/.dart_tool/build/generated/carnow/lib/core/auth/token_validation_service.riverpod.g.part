// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$tokenValidationServiceHash() =>
    r'afd0be0cfbd734dde21292d8362920eb310f7bd3';

/// Enhanced token validation service with circuit breaker and exponential backoff
/// Provides robust token handling with failure recovery mechanisms
///
/// Copied from [TokenValidationService].
@ProviderFor(TokenValidationService)
final tokenValidationServiceProvider =
    AutoDisposeNotifierProvider<
      TokenValidationService,
      TokenValidationService
    >.internal(
      TokenValidationService.new,
      name: r'tokenValidationServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$tokenValidationServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$TokenValidationService = AutoDisposeNotifier<TokenValidationService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
