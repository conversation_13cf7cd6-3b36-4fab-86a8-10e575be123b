// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$allProductsHash() => r'3b5021dfc38391a85d32bf07dad8d844bde13ba0';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Clean Products Provider - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Simple providers that use SimpleApiClient ONLY
/// No direct Supabase calls, no complex state management
/// All products provider with pagination support and fallback
///
/// Copied from [allProducts].
@ProviderFor(allProducts)
const allProductsProvider = AllProductsFamily();

/// Clean Products Provider - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Simple providers that use SimpleApiClient ONLY
/// No direct Supabase calls, no complex state management
/// All products provider with pagination support and fallback
///
/// Copied from [allProducts].
class AllProductsFamily extends Family<AsyncValue<List<ProductModel>>> {
  /// Clean Products Provider - Forever Plan Architecture
  /// Flutter (UI Only) → Go API → Supabase (Data Only)
  ///
  /// Simple providers that use SimpleApiClient ONLY
  /// No direct Supabase calls, no complex state management
  /// All products provider with pagination support and fallback
  ///
  /// Copied from [allProducts].
  const AllProductsFamily();

  /// Clean Products Provider - Forever Plan Architecture
  /// Flutter (UI Only) → Go API → Supabase (Data Only)
  ///
  /// Simple providers that use SimpleApiClient ONLY
  /// No direct Supabase calls, no complex state management
  /// All products provider with pagination support and fallback
  ///
  /// Copied from [allProducts].
  AllProductsProvider call({int page = 1, int limit = 20}) {
    return AllProductsProvider(page: page, limit: limit);
  }

  @override
  AllProductsProvider getProviderOverride(
    covariant AllProductsProvider provider,
  ) {
    return call(page: provider.page, limit: provider.limit);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'allProductsProvider';
}

/// Clean Products Provider - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Simple providers that use SimpleApiClient ONLY
/// No direct Supabase calls, no complex state management
/// All products provider with pagination support and fallback
///
/// Copied from [allProducts].
class AllProductsProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// Clean Products Provider - Forever Plan Architecture
  /// Flutter (UI Only) → Go API → Supabase (Data Only)
  ///
  /// Simple providers that use SimpleApiClient ONLY
  /// No direct Supabase calls, no complex state management
  /// All products provider with pagination support and fallback
  ///
  /// Copied from [allProducts].
  AllProductsProvider({int page = 1, int limit = 20})
    : this._internal(
        (ref) => allProducts(ref as AllProductsRef, page: page, limit: limit),
        from: allProductsProvider,
        name: r'allProductsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$allProductsHash,
        dependencies: AllProductsFamily._dependencies,
        allTransitiveDependencies: AllProductsFamily._allTransitiveDependencies,
        page: page,
        limit: limit,
      );

  AllProductsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.page,
    required this.limit,
  }) : super.internal();

  final int page;
  final int limit;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(AllProductsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: AllProductsProvider._internal(
        (ref) => create(ref as AllProductsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        page: page,
        limit: limit,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _AllProductsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AllProductsProvider &&
        other.page == page &&
        other.limit == limit;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, page.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AllProductsRef on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `page` of this provider.
  int get page;

  /// The parameter `limit` of this provider.
  int get limit;
}

class _AllProductsProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with AllProductsRef {
  _AllProductsProviderElement(super.provider);

  @override
  int get page => (origin as AllProductsProvider).page;
  @override
  int get limit => (origin as AllProductsProvider).limit;
}

String _$productByIdHash() => r'93c8ac354e63aeb88eaba1d66b07105a9188f485';

/// Single product by ID provider
///
/// Copied from [productById].
@ProviderFor(productById)
const productByIdProvider = ProductByIdFamily();

/// Single product by ID provider
///
/// Copied from [productById].
class ProductByIdFamily extends Family<AsyncValue<ProductModel?>> {
  /// Single product by ID provider
  ///
  /// Copied from [productById].
  const ProductByIdFamily();

  /// Single product by ID provider
  ///
  /// Copied from [productById].
  ProductByIdProvider call(String productId) {
    return ProductByIdProvider(productId);
  }

  @override
  ProductByIdProvider getProviderOverride(
    covariant ProductByIdProvider provider,
  ) {
    return call(provider.productId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'productByIdProvider';
}

/// Single product by ID provider
///
/// Copied from [productById].
class ProductByIdProvider extends AutoDisposeFutureProvider<ProductModel?> {
  /// Single product by ID provider
  ///
  /// Copied from [productById].
  ProductByIdProvider(String productId)
    : this._internal(
        (ref) => productById(ref as ProductByIdRef, productId),
        from: productByIdProvider,
        name: r'productByIdProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$productByIdHash,
        dependencies: ProductByIdFamily._dependencies,
        allTransitiveDependencies: ProductByIdFamily._allTransitiveDependencies,
        productId: productId,
      );

  ProductByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.productId,
  }) : super.internal();

  final String productId;

  @override
  Override overrideWith(
    FutureOr<ProductModel?> Function(ProductByIdRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProductByIdProvider._internal(
        (ref) => create(ref as ProductByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        productId: productId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ProductModel?> createElement() {
    return _ProductByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProductByIdProvider && other.productId == productId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, productId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ProductByIdRef on AutoDisposeFutureProviderRef<ProductModel?> {
  /// The parameter `productId` of this provider.
  String get productId;
}

class _ProductByIdProviderElement
    extends AutoDisposeFutureProviderElement<ProductModel?>
    with ProductByIdRef {
  _ProductByIdProviderElement(super.provider);

  @override
  String get productId => (origin as ProductByIdProvider).productId;
}

String _$productsByCategoryHash() =>
    r'14cb0ef9ae080b6fc5b010e23b23dc1daa7c95a2';

/// Products by category provider
///
/// Copied from [productsByCategory].
@ProviderFor(productsByCategory)
const productsByCategoryProvider = ProductsByCategoryFamily();

/// Products by category provider
///
/// Copied from [productsByCategory].
class ProductsByCategoryFamily extends Family<AsyncValue<List<ProductModel>>> {
  /// Products by category provider
  ///
  /// Copied from [productsByCategory].
  const ProductsByCategoryFamily();

  /// Products by category provider
  ///
  /// Copied from [productsByCategory].
  ProductsByCategoryProvider call(
    String categoryId, {
    int page = 1,
    int limit = 20,
  }) {
    return ProductsByCategoryProvider(categoryId, page: page, limit: limit);
  }

  @override
  ProductsByCategoryProvider getProviderOverride(
    covariant ProductsByCategoryProvider provider,
  ) {
    return call(
      provider.categoryId,
      page: provider.page,
      limit: provider.limit,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'productsByCategoryProvider';
}

/// Products by category provider
///
/// Copied from [productsByCategory].
class ProductsByCategoryProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// Products by category provider
  ///
  /// Copied from [productsByCategory].
  ProductsByCategoryProvider(String categoryId, {int page = 1, int limit = 20})
    : this._internal(
        (ref) => productsByCategory(
          ref as ProductsByCategoryRef,
          categoryId,
          page: page,
          limit: limit,
        ),
        from: productsByCategoryProvider,
        name: r'productsByCategoryProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$productsByCategoryHash,
        dependencies: ProductsByCategoryFamily._dependencies,
        allTransitiveDependencies:
            ProductsByCategoryFamily._allTransitiveDependencies,
        categoryId: categoryId,
        page: page,
        limit: limit,
      );

  ProductsByCategoryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categoryId,
    required this.page,
    required this.limit,
  }) : super.internal();

  final String categoryId;
  final int page;
  final int limit;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(ProductsByCategoryRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProductsByCategoryProvider._internal(
        (ref) => create(ref as ProductsByCategoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categoryId: categoryId,
        page: page,
        limit: limit,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _ProductsByCategoryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProductsByCategoryProvider &&
        other.categoryId == categoryId &&
        other.page == page &&
        other.limit == limit;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categoryId.hashCode);
    hash = _SystemHash.combine(hash, page.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ProductsByCategoryRef
    on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `categoryId` of this provider.
  String get categoryId;

  /// The parameter `page` of this provider.
  int get page;

  /// The parameter `limit` of this provider.
  int get limit;
}

class _ProductsByCategoryProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with ProductsByCategoryRef {
  _ProductsByCategoryProviderElement(super.provider);

  @override
  String get categoryId => (origin as ProductsByCategoryProvider).categoryId;
  @override
  int get page => (origin as ProductsByCategoryProvider).page;
  @override
  int get limit => (origin as ProductsByCategoryProvider).limit;
}

String _$featuredProductsHash() => r'd955ff902cce72a11c2ea093bad9587234933edb';

/// Featured products provider
///
/// Copied from [featuredProducts].
@ProviderFor(featuredProducts)
final featuredProductsProvider =
    AutoDisposeFutureProvider<List<ProductModel>>.internal(
      featuredProducts,
      name: r'featuredProductsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$featuredProductsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FeaturedProductsRef = AutoDisposeFutureProviderRef<List<ProductModel>>;
String _$userProductsHash() => r'1fa67c760097962c3e89cd797e0aee9e345afaa2';

/// User's products provider (for sellers)
///
/// Copied from [userProducts].
@ProviderFor(userProducts)
final userProductsProvider =
    AutoDisposeFutureProvider<List<ProductModel>>.internal(
      userProducts,
      name: r'userProductsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$userProductsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserProductsRef = AutoDisposeFutureProviderRef<List<ProductModel>>;
String _$searchProductsHash() => r'1440a3f2f5280d6fde109193181208c1aff56f9f';

/// Search products provider
///
/// Copied from [searchProducts].
@ProviderFor(searchProducts)
const searchProductsProvider = SearchProductsFamily();

/// Search products provider
///
/// Copied from [searchProducts].
class SearchProductsFamily extends Family<AsyncValue<List<ProductModel>>> {
  /// Search products provider
  ///
  /// Copied from [searchProducts].
  const SearchProductsFamily();

  /// Search products provider
  ///
  /// Copied from [searchProducts].
  SearchProductsProvider call(String query, {int page = 1, int limit = 20}) {
    return SearchProductsProvider(query, page: page, limit: limit);
  }

  @override
  SearchProductsProvider getProviderOverride(
    covariant SearchProductsProvider provider,
  ) {
    return call(provider.query, page: provider.page, limit: provider.limit);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'searchProductsProvider';
}

/// Search products provider
///
/// Copied from [searchProducts].
class SearchProductsProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// Search products provider
  ///
  /// Copied from [searchProducts].
  SearchProductsProvider(String query, {int page = 1, int limit = 20})
    : this._internal(
        (ref) => searchProducts(
          ref as SearchProductsRef,
          query,
          page: page,
          limit: limit,
        ),
        from: searchProductsProvider,
        name: r'searchProductsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$searchProductsHash,
        dependencies: SearchProductsFamily._dependencies,
        allTransitiveDependencies:
            SearchProductsFamily._allTransitiveDependencies,
        query: query,
        page: page,
        limit: limit,
      );

  SearchProductsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
    required this.page,
    required this.limit,
  }) : super.internal();

  final String query;
  final int page;
  final int limit;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(SearchProductsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SearchProductsProvider._internal(
        (ref) => create(ref as SearchProductsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
        page: page,
        limit: limit,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _SearchProductsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SearchProductsProvider &&
        other.query == query &&
        other.page == page &&
        other.limit == limit;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);
    hash = _SystemHash.combine(hash, page.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SearchProductsRef on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `query` of this provider.
  String get query;

  /// The parameter `page` of this provider.
  int get page;

  /// The parameter `limit` of this provider.
  int get limit;
}

class _SearchProductsProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with SearchProductsRef {
  _SearchProductsProviderElement(super.provider);

  @override
  String get query => (origin as SearchProductsProvider).query;
  @override
  int get page => (origin as SearchProductsProvider).page;
  @override
  int get limit => (origin as SearchProductsProvider).limit;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
