// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$secureTokenStorageHash() =>
    r'e5e69a815ffd6b90eeab1a23c33f70a8c9c2675c';

/// Provider for SecureTokenStorage
///
/// Copied from [secureTokenStorage].
@ProviderFor(secureTokenStorage)
final secureTokenStorageProvider =
    AutoDisposeProvider<SecureTokenStorage>.internal(
      secureTokenStorage,
      name: r'secureTokenStorageProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$secureTokenStorageHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SecureTokenStorageRef = AutoDisposeProviderRef<SecureTokenStorage>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
