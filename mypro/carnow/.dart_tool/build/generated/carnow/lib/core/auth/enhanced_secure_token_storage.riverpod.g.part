// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$enhancedSecureTokenStorageHash() =>
    r'e3f8064198a68324f06a754c351c7e35d4fc4d81';

/// Provider for enhanced secure token storage
///
/// Copied from [enhancedSecureTokenStorage].
@ProviderFor(enhancedSecureTokenStorage)
final enhancedSecureTokenStorageProvider =
    AutoDisposeProvider<EnhancedSecureTokenStorage>.internal(
      enhancedSecureTokenStorage,
      name: r'enhancedSecureTokenStorageProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$enhancedSecureTokenStorageHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef EnhancedSecureTokenStorageRef =
    AutoDisposeProviderRef<EnhancedSecureTokenStorage>;
String _$tokenStorageHash() => r'2b29be8dcb74fafcd5deb0cb8f4a671c8bd26592';

/// Provider for token storage interface
///
/// Copied from [tokenStorage].
@ProviderFor(tokenStorage)
final tokenStorageProvider = AutoDisposeProvider<ITokenStorage>.internal(
  tokenStorage,
  name: r'tokenStorageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$tokenStorageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef TokenStorageRef = AutoDisposeProviderRef<ITokenStorage>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
