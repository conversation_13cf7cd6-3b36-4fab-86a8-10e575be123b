// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$safeAuthStateHash() => r'da885510241ed3a46039f56d465270b9d1a17c46';

/// Provider that safely provides the auth state with proper error handling
///
/// Copied from [safeAuthState].
@ProviderFor(safeAuthState)
final safeAuthStateProvider = AutoDisposeProvider<AuthState>.internal(
  safeAuthState,
  name: r'safeAuthStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$safeAuthStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SafeAuthStateRef = AutoDisposeProviderRef<AuthState>;
String _$safeCurrentUserHash() => r'582902edd710ae980c6b1f5c42688aaff38a044b';

/// Provider that safely provides the current user only after auth is ready
///
/// Copied from [safeCurrentUser].
@ProviderFor(safeCurrentUser)
final safeCurrentUserProvider = AutoDisposeProvider<User?>.internal(
  safeCurrentUser,
  name: r'safeCurrentUserProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$safeCurrentUserHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SafeCurrentUserRef = AutoDisposeProviderRef<User?>;
String _$safeIsAuthenticatedHash() =>
    r'38b860f8548ea142a9c4316a6900cf4f0ba0bc75';

/// Provider that safely provides the authentication status
///
/// Copied from [safeIsAuthenticated].
@ProviderFor(safeIsAuthenticated)
final safeIsAuthenticatedProvider = AutoDisposeProvider<bool>.internal(
  safeIsAuthenticated,
  name: r'safeIsAuthenticatedProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$safeIsAuthenticatedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SafeIsAuthenticatedRef = AutoDisposeProviderRef<bool>;
String _$safeCurrentAccessTokenHash() =>
    r'273be0f490e88565697f994726526e297da251ce';

/// Provider that safely provides the current access token
///
/// Copied from [safeCurrentAccessToken].
@ProviderFor(safeCurrentAccessToken)
final safeCurrentAccessTokenProvider = AutoDisposeProvider<String?>.internal(
  safeCurrentAccessToken,
  name: r'safeCurrentAccessTokenProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$safeCurrentAccessTokenHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SafeCurrentAccessTokenRef = AutoDisposeProviderRef<String?>;
String _$authProviderInitializerHash() =>
    r'ea1648d34c81cef7eecded81392cf99e6088d33b';

/// Provider that manages the initialization of the unified auth provider
/// and ensures it's ready before other providers can safely access it
///
/// Copied from [AuthProviderInitializer].
@ProviderFor(AuthProviderInitializer)
final authProviderInitializerProvider =
    AutoDisposeNotifierProvider<
      AuthProviderInitializer,
      AuthProviderStatus
    >.internal(
      AuthProviderInitializer.new,
      name: r'authProviderInitializerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$authProviderInitializerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AuthProviderInitializer = AutoDisposeNotifier<AuthProviderStatus>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
