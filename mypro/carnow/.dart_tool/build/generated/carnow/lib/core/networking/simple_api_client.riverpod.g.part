// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$backendConfigHash() => r'160b38b4b6c986da59733f6542239a6f185f90f1';

/// Backend configuration provider
///
/// Copied from [backendConfig].
@ProviderFor(backendConfig)
final backendConfigProvider = AutoDisposeProvider<BackendConfig>.internal(
  backendConfig,
  name: r'backendConfigProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$backendConfigHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BackendConfigRef = AutoDisposeProviderRef<BackendConfig>;
String _$simpleApiClientHash() => r'f37be64bad58de88cb677d9ae69f0e4a912533a4';

/// Simple API client provider
///
/// Copied from [simpleApiClient].
@ProviderFor(simpleApiClient)
final simpleApiClientProvider = AutoDisposeProvider<SimpleApiClient>.internal(
  simpleApiClient,
  name: r'simpleApiClientProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$simpleApiClientHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SimpleApiClientRef = AutoDisposeProviderRef<SimpleApiClient>;
String _$dioInstanceHash() => r'e374d6ff210164d332704adf913587d6a27575b7';

/// Dio instance provider for lower-level access if needed
///
/// Copied from [dioInstance].
@ProviderFor(dioInstance)
final dioInstanceProvider = AutoDisposeProvider<Dio>.internal(
  dioInstance,
  name: r'dioInstanceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$dioInstanceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DioInstanceRef = AutoDisposeProviderRef<Dio>;
String _$enhancedApiClientHash() => r'7fa3e0c96252f0a9b99c1c147639a04e44b94faf';

/// Enhanced API client provider with global error handling
///
/// Copied from [enhancedApiClient].
@ProviderFor(enhancedApiClient)
final enhancedApiClientProvider =
    AutoDisposeProvider<EnhancedApiClient>.internal(
      enhancedApiClient,
      name: r'enhancedApiClientProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$enhancedApiClientHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef EnhancedApiClientRef = AutoDisposeProviderRef<EnhancedApiClient>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
