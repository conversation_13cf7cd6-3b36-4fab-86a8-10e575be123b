// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$searchApiServiceHash() => r'743a1f35e975dcb81dcf8672d5dc934d24126e25';

/// Simple Search API Service following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Copied from [searchApiService].
@ProviderFor(searchApiService)
final searchApiServiceProvider = AutoDisposeProvider<SearchApiService>.internal(
  searchApiService,
  name: r'searchApiServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$searchApiServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SearchApiServiceRef = AutoDisposeProviderRef<SearchApiService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
