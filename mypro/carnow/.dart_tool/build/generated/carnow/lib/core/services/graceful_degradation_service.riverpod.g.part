// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$gracefulDegradationServiceHash() =>
    r'3c34b3194519cb67b3801b46248ea9ad87578afc';

/// Simple Graceful Degradation Service - Forever Plan Compliant
///
/// Features (Forever Plan Approved):
/// - Basic fallback mechanisms when services are unavailable
/// - Simple connectivity checking
/// - Feature availability based on network status
/// - User-friendly error messages
///
/// What this service DOES NOT do (Forever Plan Compliance):
/// ❌ Complex offline queueing
/// ❌ Persistent data caching
/// ❌ Automatic retry mechanisms
/// ❌ Sync between databases
///
/// Copied from [gracefulDegradationService].
@ProviderFor(gracefulDegradationService)
final gracefulDegradationServiceProvider =
    AutoDisposeProvider<GracefulDegradationService>.internal(
      gracefulDegradationService,
      name: r'gracefulDegradationServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$gracefulDegradationServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GracefulDegradationServiceRef =
    AutoDisposeProviderRef<GracefulDegradationService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
