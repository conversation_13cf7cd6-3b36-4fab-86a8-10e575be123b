// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$analyticsApiServiceHash() =>
    r'9de110dc45e720d9a100458f2d1ff553404ef75d';

/// Simple Analytics API Service following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Copied from [analyticsApiService].
@ProviderFor(analyticsApiService)
final analyticsApiServiceProvider =
    AutoDisposeProvider<AnalyticsApiService>.internal(
      analyticsApiService,
      name: r'analyticsApiServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$analyticsApiServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AnalyticsApiServiceRef = AutoDisposeProviderRef<AnalyticsApiService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
