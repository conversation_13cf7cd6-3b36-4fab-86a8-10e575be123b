// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$timeoutServiceHash() => r'3c997e931b35a4e0ab8e025d7c413b620c4edd7c';

/// Timeout Service for Phase 3.2.1: Retry and Timeout Logic
///
/// Features:
/// - Configurable timeout values for different operations
/// - Timeout monitoring and statistics
/// - Graceful timeout handling
/// - Operation cancellation support
///
/// Copied from [timeoutService].
@ProviderFor(timeoutService)
final timeoutServiceProvider = AutoDisposeProvider<TimeoutService>.internal(
  timeoutService,
  name: r'timeoutServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$timeoutServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef TimeoutServiceRef = AutoDisposeProviderRef<TimeoutService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
