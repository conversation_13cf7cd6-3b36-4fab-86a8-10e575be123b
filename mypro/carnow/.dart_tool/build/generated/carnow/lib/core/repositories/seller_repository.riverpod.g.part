// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sellerRepositoryHash() => r'80c471a43b5877ccede89d852160f887b001726a';

/// See also [sellerRepository].
@ProviderFor(sellerRepository)
final sellerRepositoryProvider = Provider<SellerRepository>.internal(
  sellerRepository,
  name: r'sellerRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$sellerRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SellerRepositoryRef = ProviderRef<SellerRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
