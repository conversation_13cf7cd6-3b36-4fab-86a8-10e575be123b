// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$authInitializationServiceHash() =>
    r'f307329e328ca97f9ddfbcd356c5faadcdd04a6d';

/// Provider for AuthInitializationService
///
/// Copied from [authInitializationService].
@ProviderFor(authInitializationService)
final authInitializationServiceProvider =
    AutoDisposeProvider<AuthInitializationService>.internal(
      authInitializationService,
      name: r'authInitializationServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$authInitializationServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AuthInitializationServiceRef =
    AutoDisposeProviderRef<AuthInitializationService>;
String _$authConfigurationHash() => r'bf4f9ae43d820d98ad5d99f6ab66ff3fdd750b27';

/// Provider for auth configuration
///
/// Copied from [authConfiguration].
@ProviderFor(authConfiguration)
final authConfigurationProvider =
    AutoDisposeProvider<AuthConfiguration>.internal(
      authConfiguration,
      name: r'authConfigurationProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$authConfigurationHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AuthConfigurationRef = AutoDisposeProviderRef<AuthConfiguration>;
String _$authInitializationResultHash() =>
    r'7c6adf2abbc5477cd43e079fa15648575ae5e2b2';

/// Provider for initialization result
///
/// Copied from [authInitializationResult].
@ProviderFor(authInitializationResult)
final authInitializationResultProvider =
    AutoDisposeFutureProvider<AuthInitializationResult>.internal(
      authInitializationResult,
      name: r'authInitializationResultProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$authInitializationResultHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AuthInitializationResultRef =
    AutoDisposeFutureProviderRef<AuthInitializationResult>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
