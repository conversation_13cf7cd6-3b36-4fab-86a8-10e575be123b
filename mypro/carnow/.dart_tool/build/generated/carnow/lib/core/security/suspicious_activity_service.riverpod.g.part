// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$suspiciousActivityServiceHash() =>
    r'aa0cbcdcc795fae8daf59158f520b1e391b60e49';

/// Riverpod provider for SuspiciousActivityService
///
/// Copied from [suspiciousActivityService].
@ProviderFor(suspiciousActivityService)
final suspiciousActivityServiceProvider =
    AutoDisposeProvider<SuspiciousActivityService>.internal(
      suspiciousActivityService,
      name: r'suspiciousActivityServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$suspiciousActivityServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SuspiciousActivityServiceRef =
    AutoDisposeProviderRef<SuspiciousActivityService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
