// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$securityHeadersServiceHash() =>
    r'0b4336d2e62882fba6a2577660aa9825a2e69afd';

/// Riverpod provider for SecurityHeadersService
///
/// Copied from [securityHeadersService].
@ProviderFor(securityHeadersService)
final securityHeadersServiceProvider =
    AutoDisposeProvider<SecurityHeadersService>.internal(
      securityHeadersService,
      name: r'securityHeadersServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$securityHeadersServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SecurityHeadersServiceRef =
    AutoDisposeProviderRef<SecurityHeadersService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
