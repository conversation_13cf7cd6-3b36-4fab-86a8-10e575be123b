// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_OrderStatusHistoryModel _$OrderStatusHistoryModelFromJson(
  Map<String, dynamic> json,
) => _OrderStatusHistoryModel(
  status: $enumDecode(_$OrderStatusEnumMap, json['status']),
  timestamp: DateTime.parse(json['timestamp'] as String),
  note: json['note'] as String?,
);

Map<String, dynamic> _$OrderStatusHistoryModelToJson(
  _OrderStatusHistoryModel instance,
) => <String, dynamic>{
  'status': _$OrderStatusEnumMap[instance.status]!,
  'timestamp': instance.timestamp.toIso8601String(),
  'note': instance.note,
};

const _$OrderStatusEnumMap = {
  OrderStatus.pending: 'pending',
  OrderStatus.processing: 'processing',
  OrderStatus.confirmed: 'confirmed',
  OrderStatus.shipped: 'shipped',
  OrderStatus.delivered: 'delivered',
  OrderStatus.cancelled: 'cancelled',
  OrderStatus.returned: 'returned',
  OrderStatus.refunded: 'refunded',
};
