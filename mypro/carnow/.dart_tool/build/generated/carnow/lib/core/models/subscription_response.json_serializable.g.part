// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SubscriptionResponse _$SubscriptionResponseFromJson(
  Map<String, dynamic> json,
) => _SubscriptionResponse(
  id: json['id'] as String,
  status: json['status'] as String,
  createdAt: DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
  message: json['message'] as String?,
  storeName: json['store_name'] as String?,
  phone: json['phone'] as String?,
  city: json['city'] as String?,
  address: json['address'] as String?,
  description: json['description'] as String?,
  planType: json['plan_type'] as String?,
  price: (json['price'] as num?)?.toDouble(),
  userId: json['user_id'] as String?,
  errorCode: json['error_code'] as String?,
  errorDetails: json['error_details'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$SubscriptionResponseToJson(
  _SubscriptionResponse instance,
) => <String, dynamic>{
  'id': instance.id,
  'status': instance.status,
  'created_at': instance.createdAt.toIso8601String(),
  'updated_at': instance.updatedAt?.toIso8601String(),
  'message': instance.message,
  'store_name': instance.storeName,
  'phone': instance.phone,
  'city': instance.city,
  'address': instance.address,
  'description': instance.description,
  'plan_type': instance.planType,
  'price': instance.price,
  'user_id': instance.userId,
  'error_code': instance.errorCode,
  'error_details': instance.errorDetails,
};
