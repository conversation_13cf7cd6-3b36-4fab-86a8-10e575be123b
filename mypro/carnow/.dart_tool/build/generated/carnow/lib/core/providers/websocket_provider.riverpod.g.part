// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$webSocketServiceHash() => r'76699a05ab72155766a57d5dc6ca8a9476524587';

/// WebSocket Service Provider
///
/// Copied from [webSocketService].
@ProviderFor(webSocketService)
final webSocketServiceProvider = AutoDisposeProvider<WebSocketService>.internal(
  webSocketService,
  name: r'webSocketServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$webSocketServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WebSocketServiceRef = AutoDisposeProviderRef<WebSocketService>;
String _$webSocketMessagesHash() => r'7f9ef880aa1da0b109b2a9e8e063bad16347167c';

/// WebSocket Messages Stream Provider
///
/// Copied from [webSocketMessages].
@ProviderFor(webSocketMessages)
final webSocketMessagesProvider =
    AutoDisposeStreamProvider<WebSocketMessage>.internal(
      webSocketMessages,
      name: r'webSocketMessagesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$webSocketMessagesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WebSocketMessagesRef = AutoDisposeStreamProviderRef<WebSocketMessage>;
String _$cartUpdatesStreamHash() => r'9a3114e7490afd1c7e1ae28d0dafab18ec957a3d';

/// Cart Updates Stream Provider
///
/// Copied from [cartUpdatesStream].
@ProviderFor(cartUpdatesStream)
final cartUpdatesStreamProvider =
    AutoDisposeStreamProvider<WebSocketMessage>.internal(
      cartUpdatesStream,
      name: r'cartUpdatesStreamProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$cartUpdatesStreamHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CartUpdatesStreamRef = AutoDisposeStreamProviderRef<WebSocketMessage>;
String _$orderUpdatesStreamHash() =>
    r'cacb64a776b3c0d982320cd8509440618e115f8b';

/// Order Updates Stream Provider
///
/// Copied from [orderUpdatesStream].
@ProviderFor(orderUpdatesStream)
final orderUpdatesStreamProvider =
    AutoDisposeStreamProvider<WebSocketMessage>.internal(
      orderUpdatesStream,
      name: r'orderUpdatesStreamProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$orderUpdatesStreamHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef OrderUpdatesStreamRef = AutoDisposeStreamProviderRef<WebSocketMessage>;
String _$inventoryUpdatesStreamHash() =>
    r'9bfe2b0f57269a265a98d471968a05b75aea21c9';

/// Inventory Updates Stream Provider
///
/// Copied from [inventoryUpdatesStream].
@ProviderFor(inventoryUpdatesStream)
final inventoryUpdatesStreamProvider =
    AutoDisposeStreamProvider<WebSocketMessage>.internal(
      inventoryUpdatesStream,
      name: r'inventoryUpdatesStreamProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$inventoryUpdatesStreamHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef InventoryUpdatesStreamRef =
    AutoDisposeStreamProviderRef<WebSocketMessage>;
String _$webSocketConnectionStatusHash() =>
    r'4fbbe593b81f2272a59ad5b5fcbe03c8309413cf';

/// WebSocket Connection Status Provider
///
/// Copied from [webSocketConnectionStatus].
@ProviderFor(webSocketConnectionStatus)
final webSocketConnectionStatusProvider = AutoDisposeProvider<bool>.internal(
  webSocketConnectionStatus,
  name: r'webSocketConnectionStatusProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$webSocketConnectionStatusHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WebSocketConnectionStatusRef = AutoDisposeProviderRef<bool>;
String _$webSocketConnectionHash() =>
    r'2b822289b290a350a31359fc1f82e7aa47362907';

/// WebSocket Connection Provider
///
/// Copied from [WebSocketConnection].
@ProviderFor(WebSocketConnection)
final webSocketConnectionProvider =
    AutoDisposeAsyncNotifierProvider<WebSocketConnection, bool>.internal(
      WebSocketConnection.new,
      name: r'webSocketConnectionProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$webSocketConnectionHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$WebSocketConnection = AutoDisposeAsyncNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
