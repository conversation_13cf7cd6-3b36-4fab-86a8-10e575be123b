// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UniversalSearchState _$UniversalSearchStateFromJson(
  Map<String, dynamic> json,
) => _UniversalSearchState(
  query: json['query'] as String? ?? '',
  searchType: json['searchType'] as String? ?? 'hybrid',
  results:
      (json['results'] as List<dynamic>?)
          ?.map(
            (e) => UniversalSearchResult.fromJson(e as Map<String, dynamic>),
          )
          .toList() ??
      const [],
  suggestions:
      (json['suggestions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  filters: json['filters'] as Map<String, dynamic>? ?? const {},
  categoryFilter: json['categoryFilter'] as String?,
  categoryInsights: json['categoryInsights'] == null
      ? null
      : CategoryInsights.fromJson(
          json['categoryInsights'] as Map<String, dynamic>,
        ),
  isLoading: json['isLoading'] as bool? ?? false,
  error: json['error'] as String?,
  total: (json['total'] as num?)?.toInt() ?? 0,
  processingTime: (json['processingTime'] as num?)?.toInt() ?? 0,
);

Map<String, dynamic> _$UniversalSearchStateToJson(
  _UniversalSearchState instance,
) => <String, dynamic>{
  'query': instance.query,
  'searchType': instance.searchType,
  'results': instance.results,
  'suggestions': instance.suggestions,
  'filters': instance.filters,
  'categoryFilter': instance.categoryFilter,
  'categoryInsights': instance.categoryInsights,
  'isLoading': instance.isLoading,
  'error': instance.error,
  'total': instance.total,
  'processingTime': instance.processingTime,
};

_UniversalSearchResult _$UniversalSearchResultFromJson(
  Map<String, dynamic> json,
) => _UniversalSearchResult(
  id: json['id'] as String,
  productId: json['productId'] as String,
  categoryType: json['categoryType'] as String,
  title: json['title'] as String,
  description: json['description'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
  similarity: (json['similarity'] as num?)?.toDouble() ?? 0.0,
  textScore: (json['textScore'] as num?)?.toDouble() ?? 0.0,
  semanticScore: (json['semanticScore'] as num?)?.toDouble() ?? 0.0,
  combinedScore: (json['combinedScore'] as num?)?.toDouble() ?? 0.0,
);

Map<String, dynamic> _$UniversalSearchResultToJson(
  _UniversalSearchResult instance,
) => <String, dynamic>{
  'id': instance.id,
  'productId': instance.productId,
  'categoryType': instance.categoryType,
  'title': instance.title,
  'description': instance.description,
  'metadata': instance.metadata,
  'similarity': instance.similarity,
  'textScore': instance.textScore,
  'semanticScore': instance.semanticScore,
  'combinedScore': instance.combinedScore,
};

_UniversalSearchRequest _$UniversalSearchRequestFromJson(
  Map<String, dynamic> json,
) => _UniversalSearchRequest(
  query: json['query'] as String,
  searchType: json['searchType'] as String? ?? 'hybrid',
  categoryFilter: json['categoryFilter'] as String?,
  filters: json['filters'] as Map<String, dynamic>? ?? const {},
  limit: (json['limit'] as num?)?.toInt() ?? 20,
  matchThreshold: (json['matchThreshold'] as num?)?.toDouble() ?? 0.75,
  textWeight: (json['textWeight'] as num?)?.toDouble() ?? 0.3,
  semanticWeight: (json['semanticWeight'] as num?)?.toDouble() ?? 0.7,
);

Map<String, dynamic> _$UniversalSearchRequestToJson(
  _UniversalSearchRequest instance,
) => <String, dynamic>{
  'query': instance.query,
  'searchType': instance.searchType,
  'categoryFilter': instance.categoryFilter,
  'filters': instance.filters,
  'limit': instance.limit,
  'matchThreshold': instance.matchThreshold,
  'textWeight': instance.textWeight,
  'semanticWeight': instance.semanticWeight,
};

_UniversalSearchResponse _$UniversalSearchResponseFromJson(
  Map<String, dynamic> json,
) => _UniversalSearchResponse(
  query: json['query'] as String,
  searchType: json['searchType'] as String? ?? 'hybrid',
  results:
      (json['results'] as List<dynamic>?)
          ?.map(
            (e) => UniversalSearchResult.fromJson(e as Map<String, dynamic>),
          )
          .toList() ??
      const [],
  suggestions:
      (json['suggestions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  total: (json['total'] as num?)?.toInt() ?? 0,
  processingTime: (json['processingTime'] as num?)?.toInt() ?? 0,
  categoryInsights: json['categoryInsights'] == null
      ? null
      : CategoryInsights.fromJson(
          json['categoryInsights'] as Map<String, dynamic>,
        ),
);

Map<String, dynamic> _$UniversalSearchResponseToJson(
  _UniversalSearchResponse instance,
) => <String, dynamic>{
  'query': instance.query,
  'searchType': instance.searchType,
  'results': instance.results,
  'suggestions': instance.suggestions,
  'total': instance.total,
  'processingTime': instance.processingTime,
  'categoryInsights': instance.categoryInsights,
};

_UniversalCategory _$UniversalCategoryFromJson(Map<String, dynamic> json) =>
    _UniversalCategory(
      id: json['id'] as String,
      name: json['name'] as String,
      nameAr: json['nameAr'] as String?,
      nameEn: json['nameEn'] as String?,
      categoryType: json['categoryType'] as String,
      parentId: json['parentId'] as String?,
      level: (json['level'] as num?)?.toInt() ?? 0,
      sortOrder: (json['sortOrder'] as num?)?.toInt() ?? 0,
      isActive: json['isActive'] as bool? ?? true,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$UniversalCategoryToJson(_UniversalCategory instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'nameAr': instance.nameAr,
      'nameEn': instance.nameEn,
      'categoryType': instance.categoryType,
      'parentId': instance.parentId,
      'level': instance.level,
      'sortOrder': instance.sortOrder,
      'isActive': instance.isActive,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

_CategoryFilter _$CategoryFilterFromJson(Map<String, dynamic> json) =>
    _CategoryFilter(
      id: json['id'] as String,
      categoryId: json['categoryId'] as String,
      filterName: json['filterName'] as String,
      filterType: json['filterType'] as String,
      filterOptions: json['filterOptions'] as Map<String, dynamic>? ?? const {},
      isRequired: json['isRequired'] as bool? ?? false,
      sortOrder: (json['sortOrder'] as num?)?.toInt() ?? 0,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$CategoryFilterToJson(_CategoryFilter instance) =>
    <String, dynamic>{
      'id': instance.id,
      'categoryId': instance.categoryId,
      'filterName': instance.filterName,
      'filterType': instance.filterType,
      'filterOptions': instance.filterOptions,
      'isRequired': instance.isRequired,
      'sortOrder': instance.sortOrder,
      'createdAt': instance.createdAt?.toIso8601String(),
    };

_CategoryInsights _$CategoryInsightsFromJson(Map<String, dynamic> json) =>
    _CategoryInsights(
      distribution:
          (json['distribution'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, (e as num).toInt()),
          ) ??
          const {},
      categoryInfo:
          (json['categoryInfo'] as List<dynamic>?)
              ?.map((e) => CategoryInfo.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      totalCategories: (json['totalCategories'] as num?)?.toInt() ?? 0,
      dominantCategory: json['dominantCategory'] as String?,
    );

Map<String, dynamic> _$CategoryInsightsToJson(_CategoryInsights instance) =>
    <String, dynamic>{
      'distribution': instance.distribution,
      'categoryInfo': instance.categoryInfo,
      'totalCategories': instance.totalCategories,
      'dominantCategory': instance.dominantCategory,
    };

_CategoryInfo _$CategoryInfoFromJson(Map<String, dynamic> json) =>
    _CategoryInfo(
      categoryType: json['categoryType'] as String,
      name: json['name'] as String,
      nameAr: json['nameAr'] as String?,
      nameEn: json['nameEn'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$CategoryInfoToJson(_CategoryInfo instance) =>
    <String, dynamic>{
      'categoryType': instance.categoryType,
      'name': instance.name,
      'nameAr': instance.nameAr,
      'nameEn': instance.nameEn,
      'metadata': instance.metadata,
    };

_FilterState _$FilterStateFromJson(Map<String, dynamic> json) => _FilterState(
  activeFilters: json['activeFilters'] as Map<String, dynamic>? ?? const {},
  availableFilters:
      (json['availableFilters'] as List<dynamic>?)
          ?.map((e) => CategoryFilter.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  isLoading: json['isLoading'] as bool? ?? false,
  error: json['error'] as String?,
);

Map<String, dynamic> _$FilterStateToJson(_FilterState instance) =>
    <String, dynamic>{
      'activeFilters': instance.activeFilters,
      'availableFilters': instance.availableFilters,
      'isLoading': instance.isLoading,
      'error': instance.error,
    };

_SearchSettings _$SearchSettingsFromJson(Map<String, dynamic> json) =>
    _SearchSettings(
      defaultSearchType: json['defaultSearchType'] as String? ?? 'hybrid',
      defaultMatchThreshold:
          (json['defaultMatchThreshold'] as num?)?.toDouble() ?? 0.75,
      defaultTextWeight: (json['defaultTextWeight'] as num?)?.toDouble() ?? 0.3,
      defaultSemanticWeight:
          (json['defaultSemanticWeight'] as num?)?.toDouble() ?? 0.7,
      defaultLimit: (json['defaultLimit'] as num?)?.toInt() ?? 20,
      enableSuggestions: json['enableSuggestions'] as bool? ?? true,
      enableHistory: json['enableHistory'] as bool? ?? true,
      enableFilters: json['enableFilters'] as bool? ?? true,
    );

Map<String, dynamic> _$SearchSettingsToJson(_SearchSettings instance) =>
    <String, dynamic>{
      'defaultSearchType': instance.defaultSearchType,
      'defaultMatchThreshold': instance.defaultMatchThreshold,
      'defaultTextWeight': instance.defaultTextWeight,
      'defaultSemanticWeight': instance.defaultSemanticWeight,
      'defaultLimit': instance.defaultLimit,
      'enableSuggestions': instance.enableSuggestions,
      'enableHistory': instance.enableHistory,
      'enableFilters': instance.enableFilters,
    };
