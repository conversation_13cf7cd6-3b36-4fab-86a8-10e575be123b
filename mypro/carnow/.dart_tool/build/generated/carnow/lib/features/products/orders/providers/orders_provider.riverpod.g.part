// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$myOrdersHash() => r'e7f52541cf563b9f7f4262e3fc3366efba866c66';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Get user's orders from Go backend
/// ENDPOINT: GET /api/v1/orders?userId={userId}
///
/// Copied from [myOrders].
@ProviderFor(myOrders)
const myOrdersProvider = MyOrdersFamily();

/// Get user's orders from Go backend
/// ENDPOINT: GET /api/v1/orders?userId={userId}
///
/// Copied from [myOrders].
class MyOrdersFamily extends Family<AsyncValue<List<OrderModel>>> {
  /// Get user's orders from Go backend
  /// ENDPOINT: GET /api/v1/orders?userId={userId}
  ///
  /// Copied from [myOrders].
  const MyOrdersFamily();

  /// Get user's orders from Go backend
  /// ENDPOINT: GET /api/v1/orders?userId={userId}
  ///
  /// Copied from [myOrders].
  MyOrdersProvider call(String userId) {
    return MyOrdersProvider(userId);
  }

  @override
  MyOrdersProvider getProviderOverride(covariant MyOrdersProvider provider) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'myOrdersProvider';
}

/// Get user's orders from Go backend
/// ENDPOINT: GET /api/v1/orders?userId={userId}
///
/// Copied from [myOrders].
class MyOrdersProvider extends AutoDisposeFutureProvider<List<OrderModel>> {
  /// Get user's orders from Go backend
  /// ENDPOINT: GET /api/v1/orders?userId={userId}
  ///
  /// Copied from [myOrders].
  MyOrdersProvider(String userId)
    : this._internal(
        (ref) => myOrders(ref as MyOrdersRef, userId),
        from: myOrdersProvider,
        name: r'myOrdersProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$myOrdersHash,
        dependencies: MyOrdersFamily._dependencies,
        allTransitiveDependencies: MyOrdersFamily._allTransitiveDependencies,
        userId: userId,
      );

  MyOrdersProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    FutureOr<List<OrderModel>> Function(MyOrdersRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: MyOrdersProvider._internal(
        (ref) => create(ref as MyOrdersRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<OrderModel>> createElement() {
    return _MyOrdersProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is MyOrdersProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin MyOrdersRef on AutoDisposeFutureProviderRef<List<OrderModel>> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _MyOrdersProviderElement
    extends AutoDisposeFutureProviderElement<List<OrderModel>>
    with MyOrdersRef {
  _MyOrdersProviderElement(super.provider);

  @override
  String get userId => (origin as MyOrdersProvider).userId;
}

String _$paginatedMyOrdersHash() => r'cb3a8873556afaa78c31759e33c01a0016f385ee';

/// Get user orders with pagination from Go backend
/// ENDPOINT: GET /api/v1/orders?userId={userId}&page={page}&limit={pageSize}
///
/// Copied from [paginatedMyOrders].
@ProviderFor(paginatedMyOrders)
const paginatedMyOrdersProvider = PaginatedMyOrdersFamily();

/// Get user orders with pagination from Go backend
/// ENDPOINT: GET /api/v1/orders?userId={userId}&page={page}&limit={pageSize}
///
/// Copied from [paginatedMyOrders].
class PaginatedMyOrdersFamily extends Family<AsyncValue<List<OrderModel>>> {
  /// Get user orders with pagination from Go backend
  /// ENDPOINT: GET /api/v1/orders?userId={userId}&page={page}&limit={pageSize}
  ///
  /// Copied from [paginatedMyOrders].
  const PaginatedMyOrdersFamily();

  /// Get user orders with pagination from Go backend
  /// ENDPOINT: GET /api/v1/orders?userId={userId}&page={page}&limit={pageSize}
  ///
  /// Copied from [paginatedMyOrders].
  PaginatedMyOrdersProvider call(Map<String, dynamic> params) {
    return PaginatedMyOrdersProvider(params);
  }

  @override
  PaginatedMyOrdersProvider getProviderOverride(
    covariant PaginatedMyOrdersProvider provider,
  ) {
    return call(provider.params);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'paginatedMyOrdersProvider';
}

/// Get user orders with pagination from Go backend
/// ENDPOINT: GET /api/v1/orders?userId={userId}&page={page}&limit={pageSize}
///
/// Copied from [paginatedMyOrders].
class PaginatedMyOrdersProvider
    extends AutoDisposeFutureProvider<List<OrderModel>> {
  /// Get user orders with pagination from Go backend
  /// ENDPOINT: GET /api/v1/orders?userId={userId}&page={page}&limit={pageSize}
  ///
  /// Copied from [paginatedMyOrders].
  PaginatedMyOrdersProvider(Map<String, dynamic> params)
    : this._internal(
        (ref) => paginatedMyOrders(ref as PaginatedMyOrdersRef, params),
        from: paginatedMyOrdersProvider,
        name: r'paginatedMyOrdersProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$paginatedMyOrdersHash,
        dependencies: PaginatedMyOrdersFamily._dependencies,
        allTransitiveDependencies:
            PaginatedMyOrdersFamily._allTransitiveDependencies,
        params: params,
      );

  PaginatedMyOrdersProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.params,
  }) : super.internal();

  final Map<String, dynamic> params;

  @override
  Override overrideWith(
    FutureOr<List<OrderModel>> Function(PaginatedMyOrdersRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: PaginatedMyOrdersProvider._internal(
        (ref) => create(ref as PaginatedMyOrdersRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        params: params,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<OrderModel>> createElement() {
    return _PaginatedMyOrdersProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is PaginatedMyOrdersProvider && other.params == params;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, params.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin PaginatedMyOrdersRef on AutoDisposeFutureProviderRef<List<OrderModel>> {
  /// The parameter `params` of this provider.
  Map<String, dynamic> get params;
}

class _PaginatedMyOrdersProviderElement
    extends AutoDisposeFutureProviderElement<List<OrderModel>>
    with PaginatedMyOrdersRef {
  _PaginatedMyOrdersProviderElement(super.provider);

  @override
  Map<String, dynamic> get params =>
      (origin as PaginatedMyOrdersProvider).params;
}

String _$sellerSalesHash() => r'c4fff97bde19fb03f65b3b5cdb2716e11ad4625d';

/// Get seller's sales from Go backend
/// ENDPOINT: GET /api/v1/seller/orders?sellerId={sellerId}&page={page}&limit={pageSize}
///
/// Copied from [sellerSales].
@ProviderFor(sellerSales)
const sellerSalesProvider = SellerSalesFamily();

/// Get seller's sales from Go backend
/// ENDPOINT: GET /api/v1/seller/orders?sellerId={sellerId}&page={page}&limit={pageSize}
///
/// Copied from [sellerSales].
class SellerSalesFamily extends Family<AsyncValue<List<OrderModel>>> {
  /// Get seller's sales from Go backend
  /// ENDPOINT: GET /api/v1/seller/orders?sellerId={sellerId}&page={page}&limit={pageSize}
  ///
  /// Copied from [sellerSales].
  const SellerSalesFamily();

  /// Get seller's sales from Go backend
  /// ENDPOINT: GET /api/v1/seller/orders?sellerId={sellerId}&page={page}&limit={pageSize}
  ///
  /// Copied from [sellerSales].
  SellerSalesProvider call(Map<String, dynamic> params) {
    return SellerSalesProvider(params);
  }

  @override
  SellerSalesProvider getProviderOverride(
    covariant SellerSalesProvider provider,
  ) {
    return call(provider.params);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'sellerSalesProvider';
}

/// Get seller's sales from Go backend
/// ENDPOINT: GET /api/v1/seller/orders?sellerId={sellerId}&page={page}&limit={pageSize}
///
/// Copied from [sellerSales].
class SellerSalesProvider extends AutoDisposeFutureProvider<List<OrderModel>> {
  /// Get seller's sales from Go backend
  /// ENDPOINT: GET /api/v1/seller/orders?sellerId={sellerId}&page={page}&limit={pageSize}
  ///
  /// Copied from [sellerSales].
  SellerSalesProvider(Map<String, dynamic> params)
    : this._internal(
        (ref) => sellerSales(ref as SellerSalesRef, params),
        from: sellerSalesProvider,
        name: r'sellerSalesProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$sellerSalesHash,
        dependencies: SellerSalesFamily._dependencies,
        allTransitiveDependencies: SellerSalesFamily._allTransitiveDependencies,
        params: params,
      );

  SellerSalesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.params,
  }) : super.internal();

  final Map<String, dynamic> params;

  @override
  Override overrideWith(
    FutureOr<List<OrderModel>> Function(SellerSalesRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SellerSalesProvider._internal(
        (ref) => create(ref as SellerSalesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        params: params,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<OrderModel>> createElement() {
    return _SellerSalesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SellerSalesProvider && other.params == params;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, params.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SellerSalesRef on AutoDisposeFutureProviderRef<List<OrderModel>> {
  /// The parameter `params` of this provider.
  Map<String, dynamic> get params;
}

class _SellerSalesProviderElement
    extends AutoDisposeFutureProviderElement<List<OrderModel>>
    with SellerSalesRef {
  _SellerSalesProviderElement(super.provider);

  @override
  Map<String, dynamic> get params => (origin as SellerSalesProvider).params;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
