// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SellerSubscriptionRequest _$SellerSubscriptionRequestFromJson(
  Map<String, dynamic> json,
) => _SellerSubscriptionRequest(
  id: json['id'] as String,
  sellerId: json['seller_id'] as String,
  planId: json['plan_id'] as String,
  requestedTier: json['requested_tier'] as String,
  billingCycle: json['billing_cycle'] as String,
  status: json['status'] as String,
  requestDate: DateTime.parse(json['request_date'] as String),
  approvedDate: json['approved_date'] == null
      ? null
      : DateTime.parse(json['approved_date'] as String),
  rejectedDate: json['rejected_date'] == null
      ? null
      : DateTime.parse(json['rejected_date'] as String),
  adminId: json['admin_id'] as String?,
  adminNotes: json['admin_notes'] as String?,
  rejectionReason: json['rejection_reason'] as String?,
  requestedPriceLD: (json['requested_price_l_d'] as num).toDouble(),
  paymentMethodId: json['payment_method_id'] as String?,
  sellerInfo:
      json['seller_info'] as Map<String, dynamic>? ?? const <String, dynamic>{},
  businessDocuments:
      json['business_documents'] as Map<String, dynamic>? ??
      const <String, dynamic>{},
  requiresDocumentVerification:
      json['requires_document_verification'] as bool? ?? false,
  priority: (json['priority'] as num?)?.toInt(),
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
);

Map<String, dynamic> _$SellerSubscriptionRequestToJson(
  _SellerSubscriptionRequest instance,
) => <String, dynamic>{
  'id': instance.id,
  'seller_id': instance.sellerId,
  'plan_id': instance.planId,
  'requested_tier': instance.requestedTier,
  'billing_cycle': instance.billingCycle,
  'status': instance.status,
  'request_date': instance.requestDate.toIso8601String(),
  'approved_date': instance.approvedDate?.toIso8601String(),
  'rejected_date': instance.rejectedDate?.toIso8601String(),
  'admin_id': instance.adminId,
  'admin_notes': instance.adminNotes,
  'rejection_reason': instance.rejectionReason,
  'requested_price_l_d': instance.requestedPriceLD,
  'payment_method_id': instance.paymentMethodId,
  'seller_info': instance.sellerInfo,
  'business_documents': instance.businessDocuments,
  'requires_document_verification': instance.requiresDocumentVerification,
  'priority': instance.priority,
};

_SellerSubscriptionRequestReview _$SellerSubscriptionRequestReviewFromJson(
  Map<String, dynamic> json,
) => _SellerSubscriptionRequestReview(
  id: json['id'] as String,
  requestId: json['request_id'] as String,
  adminId: json['admin_id'] as String,
  reviewDate: DateTime.parse(json['review_date'] as String),
  decision: json['decision'] as String,
  notes: json['notes'] as String?,
  rejectionReason: json['rejection_reason'] as String?,
  reviewData:
      json['review_data'] as Map<String, dynamic>? ?? const <String, dynamic>{},
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
);

Map<String, dynamic> _$SellerSubscriptionRequestReviewToJson(
  _SellerSubscriptionRequestReview instance,
) => <String, dynamic>{
  'id': instance.id,
  'request_id': instance.requestId,
  'admin_id': instance.adminId,
  'review_date': instance.reviewDate.toIso8601String(),
  'decision': instance.decision,
  'notes': instance.notes,
  'rejection_reason': instance.rejectionReason,
  'review_data': instance.reviewData,
};
