// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SalesStatsModel _$SalesStatsModelFromJson(Map<String, dynamic> json) =>
    _SalesStatsModel(
      totalRevenue: (json['totalRevenue'] as num?)?.toDouble(),
      totalOrders: (json['totalOrders'] as num?)?.toInt(),
      pendingOrders: (json['pendingOrders'] as num?)?.toInt(),
      completedOrders: (json['completedOrders'] as num?)?.toInt(),
      dailyRevenue: (json['dailyRevenue'] as num?)?.toDouble() ?? 0,
      weeklyRevenue: (json['weeklyRevenue'] as num?)?.toDouble() ?? 0,
      monthlyRevenue: (json['monthlyRevenue'] as num?)?.toDouble() ?? 0,
      dailyOrders: (json['dailyOrders'] as num?)?.toInt() ?? 0,
      weeklyOrders: (json['weeklyOrders'] as num?)?.toInt() ?? 0,
      monthlyOrders: (json['monthlyOrders'] as num?)?.toInt() ?? 0,
      topSellingProducts:
          (json['topSellingProducts'] as List<dynamic>?)
              ?.map((e) => ProductSaleStats.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      recentSalesData:
          (json['recentSalesData'] as List<dynamic>?)
              ?.map((e) => DailySales.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$SalesStatsModelToJson(_SalesStatsModel instance) =>
    <String, dynamic>{
      'totalRevenue': instance.totalRevenue,
      'totalOrders': instance.totalOrders,
      'pendingOrders': instance.pendingOrders,
      'completedOrders': instance.completedOrders,
      'dailyRevenue': instance.dailyRevenue,
      'weeklyRevenue': instance.weeklyRevenue,
      'monthlyRevenue': instance.monthlyRevenue,
      'dailyOrders': instance.dailyOrders,
      'weeklyOrders': instance.weeklyOrders,
      'monthlyOrders': instance.monthlyOrders,
      'topSellingProducts': instance.topSellingProducts,
      'recentSalesData': instance.recentSalesData,
    };
