// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$cachedProductHash() => r'691031667c381407f3843cf02905c65c38c9bc2f';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Enhanced products provider with caching for frequently accessed products
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [cachedProduct].
@ProviderFor(cachedProduct)
const cachedProductProvider = CachedProductFamily();

/// Enhanced products provider with caching for frequently accessed products
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [cachedProduct].
class CachedProductFamily extends Family<AsyncValue<ProductModel?>> {
  /// Enhanced products provider with caching for frequently accessed products
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [cachedProduct].
  const CachedProductFamily();

  /// Enhanced products provider with caching for frequently accessed products
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [cachedProduct].
  CachedProductProvider call(String productId) {
    return CachedProductProvider(productId);
  }

  @override
  CachedProductProvider getProviderOverride(
    covariant CachedProductProvider provider,
  ) {
    return call(provider.productId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'cachedProductProvider';
}

/// Enhanced products provider with caching for frequently accessed products
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [cachedProduct].
class CachedProductProvider extends AutoDisposeFutureProvider<ProductModel?> {
  /// Enhanced products provider with caching for frequently accessed products
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [cachedProduct].
  CachedProductProvider(String productId)
    : this._internal(
        (ref) => cachedProduct(ref as CachedProductRef, productId),
        from: cachedProductProvider,
        name: r'cachedProductProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$cachedProductHash,
        dependencies: CachedProductFamily._dependencies,
        allTransitiveDependencies:
            CachedProductFamily._allTransitiveDependencies,
        productId: productId,
      );

  CachedProductProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.productId,
  }) : super.internal();

  final String productId;

  @override
  Override overrideWith(
    FutureOr<ProductModel?> Function(CachedProductRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CachedProductProvider._internal(
        (ref) => create(ref as CachedProductRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        productId: productId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ProductModel?> createElement() {
    return _CachedProductProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CachedProductProvider && other.productId == productId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, productId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CachedProductRef on AutoDisposeFutureProviderRef<ProductModel?> {
  /// The parameter `productId` of this provider.
  String get productId;
}

class _CachedProductProviderElement
    extends AutoDisposeFutureProviderElement<ProductModel?>
    with CachedProductRef {
  _CachedProductProviderElement(super.provider);

  @override
  String get productId => (origin as CachedProductProvider).productId;
}

String _$frequentlyAccessedProductsHash() =>
    r'19db892836bdee559895512ccf48207913af125b';

/// Provider for frequently accessed products (based on analytics)
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [frequentlyAccessedProducts].
@ProviderFor(frequentlyAccessedProducts)
final frequentlyAccessedProductsProvider =
    AutoDisposeFutureProvider<List<ProductModel>>.internal(
      frequentlyAccessedProducts,
      name: r'frequentlyAccessedProductsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$frequentlyAccessedProductsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FrequentlyAccessedProductsRef =
    AutoDisposeFutureProviderRef<List<ProductModel>>;
String _$popularProductsWithCacheHash() =>
    r'e3e17912c8c4967b3ee5540cfa190b7949ef5d61';

/// Provider for popular products with caching
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [popularProductsWithCache].
@ProviderFor(popularProductsWithCache)
final popularProductsWithCacheProvider =
    AutoDisposeFutureProvider<List<ProductModel>>.internal(
      popularProductsWithCache,
      name: r'popularProductsWithCacheProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$popularProductsWithCacheHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PopularProductsWithCacheRef =
    AutoDisposeFutureProviderRef<List<ProductModel>>;
String _$cacheManagerHash() => r'1700a3202f91a3455885c4420cfb7a6cbc16c509';

/// Provider to manage cache invalidation
///
/// Copied from [CacheManager].
@ProviderFor(CacheManager)
final cacheManagerProvider =
    AutoDisposeNotifierProvider<CacheManager, bool>.internal(
      CacheManager.new,
      name: r'cacheManagerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$cacheManagerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CacheManager = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
