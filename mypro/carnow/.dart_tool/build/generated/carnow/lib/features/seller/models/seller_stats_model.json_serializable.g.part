// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SellerStatsModel _$SellerStatsModelFromJson(Map<String, dynamic> json) =>
    _SellerStatsModel(
      totalProducts: (json['totalProducts'] as num?)?.toInt() ?? 0,
      totalViews: (json['totalViews'] as num?)?.toInt() ?? 0,
      activeListings: (json['activeListings'] as num?)?.toInt() ?? 0,
      pendingListings: (json['pendingListings'] as num?)?.toInt() ?? 0,
      totalOrders: (json['totalOrders'] as num?)?.toInt() ?? 0,
      pendingOrders: (json['pendingOrders'] as num?)?.toInt() ?? 0,
      totalRevenue: (json['totalRevenue'] as num?)?.toDouble() ?? 0,
      unreadMessages: (json['unreadMessages'] as num?)?.toInt() ?? 0,
      lastUpdated: json['lastUpdated'] == null
          ? null
          : DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$SellerStatsModelToJson(_SellerStatsModel instance) =>
    <String, dynamic>{
      'totalProducts': instance.totalProducts,
      'totalViews': instance.totalViews,
      'activeListings': instance.activeListings,
      'pendingListings': instance.pendingListings,
      'totalOrders': instance.totalOrders,
      'pendingOrders': instance.pendingOrders,
      'totalRevenue': instance.totalRevenue,
      'unreadMessages': instance.unreadMessages,
      'lastUpdated': instance.lastUpdated?.toIso8601String(),
    };
