// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$carRepositoryHash() => r'e79d247aa6103963a9171288523997b50fe256bd';

/// See also [carRepository].
@ProviderFor(carRepository)
final carRepositoryProvider = AutoDisposeProvider<CarRepository>.internal(
  carRepository,
  name: r'carRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$carRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CarRepositoryRef = AutoDisposeProviderRef<CarRepository>;
String _$vehicleMakesHash() => r'9edf8e77589958cf532996ee4be1046ed80e0427';

/// See also [vehicleMakes].
@ProviderFor(vehicleMakes)
final vehicleMakesProvider =
    AutoDisposeFutureProvider<List<VehicleMake>>.internal(
      vehicleMakes,
      name: r'vehicleMakesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$vehicleMakesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef VehicleMakesRef = AutoDisposeFutureProviderRef<List<VehicleMake>>;
String _$vehicleModelsHash() => r'614492680e0947dbc8c875fcf5e2682a20587b52';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [vehicleModels].
@ProviderFor(vehicleModels)
const vehicleModelsProvider = VehicleModelsFamily();

/// See also [vehicleModels].
class VehicleModelsFamily extends Family<AsyncValue<List<VehicleModel>>> {
  /// See also [vehicleModels].
  const VehicleModelsFamily();

  /// See also [vehicleModels].
  VehicleModelsProvider call(int makeId) {
    return VehicleModelsProvider(makeId);
  }

  @override
  VehicleModelsProvider getProviderOverride(
    covariant VehicleModelsProvider provider,
  ) {
    return call(provider.makeId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'vehicleModelsProvider';
}

/// See also [vehicleModels].
class VehicleModelsProvider
    extends AutoDisposeFutureProvider<List<VehicleModel>> {
  /// See also [vehicleModels].
  VehicleModelsProvider(int makeId)
    : this._internal(
        (ref) => vehicleModels(ref as VehicleModelsRef, makeId),
        from: vehicleModelsProvider,
        name: r'vehicleModelsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$vehicleModelsHash,
        dependencies: VehicleModelsFamily._dependencies,
        allTransitiveDependencies:
            VehicleModelsFamily._allTransitiveDependencies,
        makeId: makeId,
      );

  VehicleModelsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.makeId,
  }) : super.internal();

  final int makeId;

  @override
  Override overrideWith(
    FutureOr<List<VehicleModel>> Function(VehicleModelsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: VehicleModelsProvider._internal(
        (ref) => create(ref as VehicleModelsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        makeId: makeId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<VehicleModel>> createElement() {
    return _VehicleModelsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is VehicleModelsProvider && other.makeId == makeId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, makeId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin VehicleModelsRef on AutoDisposeFutureProviderRef<List<VehicleModel>> {
  /// The parameter `makeId` of this provider.
  int get makeId;
}

class _VehicleModelsProviderElement
    extends AutoDisposeFutureProviderElement<List<VehicleModel>>
    with VehicleModelsRef {
  _VehicleModelsProviderElement(super.provider);

  @override
  int get makeId => (origin as VehicleModelsProvider).makeId;
}

String _$carProviderHash() => r'e5d4337b77d59a3131426cc92611e98ede09348e';

/// See also [carProvider].
@ProviderFor(carProvider)
const carProviderProvider = CarProviderFamily();

/// See also [carProvider].
class CarProviderFamily extends Family<AsyncValue<CarModel>> {
  /// See also [carProvider].
  const CarProviderFamily();

  /// See also [carProvider].
  CarProviderProvider call(String carId) {
    return CarProviderProvider(carId);
  }

  @override
  CarProviderProvider getProviderOverride(
    covariant CarProviderProvider provider,
  ) {
    return call(provider.carId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'carProviderProvider';
}

/// See also [carProvider].
class CarProviderProvider extends AutoDisposeFutureProvider<CarModel> {
  /// See also [carProvider].
  CarProviderProvider(String carId)
    : this._internal(
        (ref) => carProvider(ref as CarProviderRef, carId),
        from: carProviderProvider,
        name: r'carProviderProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$carProviderHash,
        dependencies: CarProviderFamily._dependencies,
        allTransitiveDependencies: CarProviderFamily._allTransitiveDependencies,
        carId: carId,
      );

  CarProviderProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.carId,
  }) : super.internal();

  final String carId;

  @override
  Override overrideWith(
    FutureOr<CarModel> Function(CarProviderRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CarProviderProvider._internal(
        (ref) => create(ref as CarProviderRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        carId: carId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<CarModel> createElement() {
    return _CarProviderProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CarProviderProvider && other.carId == carId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, carId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CarProviderRef on AutoDisposeFutureProviderRef<CarModel> {
  /// The parameter `carId` of this provider.
  String get carId;
}

class _CarProviderProviderElement
    extends AutoDisposeFutureProviderElement<CarModel>
    with CarProviderRef {
  _CarProviderProviderElement(super.provider);

  @override
  String get carId => (origin as CarProviderProvider).carId;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
