// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$addCarNotifierHash() => r'23c530752cd288051dd9bf62c041bbacb19b90bf';

/// See also [AddCarNotifier].
@ProviderFor(AddCarNotifier)
final addCarNotifierProvider =
    AutoDisposeAsyncNotifierProvider<AddCarNotifier, void>.internal(
      AddCarNotifier.new,
      name: r'addCarNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$addCarNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AddCarNotifier = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
