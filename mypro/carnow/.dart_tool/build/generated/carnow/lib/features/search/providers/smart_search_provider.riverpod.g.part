// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$smartSearchSuggestionsHash() =>
    r'2dcb8fd22648021c00b2f5f2c8d0b07d091ed268';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Smart Search Provider - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// ✅ Uses Go Backend API ONLY for search
/// ✅ NO direct Supabase calls
/// ✅ Clean async/await patterns
/// Provider for search suggestions with smart recommendations
///
/// Copied from [smartSearchSuggestions].
@ProviderFor(smartSearchSuggestions)
const smartSearchSuggestionsProvider = SmartSearchSuggestionsFamily();

/// Smart Search Provider - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// ✅ Uses Go Backend API ONLY for search
/// ✅ NO direct Supabase calls
/// ✅ Clean async/await patterns
/// Provider for search suggestions with smart recommendations
///
/// Copied from [smartSearchSuggestions].
class SmartSearchSuggestionsFamily
    extends Family<AsyncValue<List<SearchSuggestion>>> {
  /// Smart Search Provider - Forever Plan Architecture
  /// Flutter (UI Only) → Go API → Supabase (Data Only)
  ///
  /// ✅ Uses Go Backend API ONLY for search
  /// ✅ NO direct Supabase calls
  /// ✅ Clean async/await patterns
  /// Provider for search suggestions with smart recommendations
  ///
  /// Copied from [smartSearchSuggestions].
  const SmartSearchSuggestionsFamily();

  /// Smart Search Provider - Forever Plan Architecture
  /// Flutter (UI Only) → Go API → Supabase (Data Only)
  ///
  /// ✅ Uses Go Backend API ONLY for search
  /// ✅ NO direct Supabase calls
  /// ✅ Clean async/await patterns
  /// Provider for search suggestions with smart recommendations
  ///
  /// Copied from [smartSearchSuggestions].
  SmartSearchSuggestionsProvider call(String query) {
    return SmartSearchSuggestionsProvider(query);
  }

  @override
  SmartSearchSuggestionsProvider getProviderOverride(
    covariant SmartSearchSuggestionsProvider provider,
  ) {
    return call(provider.query);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'smartSearchSuggestionsProvider';
}

/// Smart Search Provider - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// ✅ Uses Go Backend API ONLY for search
/// ✅ NO direct Supabase calls
/// ✅ Clean async/await patterns
/// Provider for search suggestions with smart recommendations
///
/// Copied from [smartSearchSuggestions].
class SmartSearchSuggestionsProvider
    extends AutoDisposeFutureProvider<List<SearchSuggestion>> {
  /// Smart Search Provider - Forever Plan Architecture
  /// Flutter (UI Only) → Go API → Supabase (Data Only)
  ///
  /// ✅ Uses Go Backend API ONLY for search
  /// ✅ NO direct Supabase calls
  /// ✅ Clean async/await patterns
  /// Provider for search suggestions with smart recommendations
  ///
  /// Copied from [smartSearchSuggestions].
  SmartSearchSuggestionsProvider(String query)
    : this._internal(
        (ref) =>
            smartSearchSuggestions(ref as SmartSearchSuggestionsRef, query),
        from: smartSearchSuggestionsProvider,
        name: r'smartSearchSuggestionsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$smartSearchSuggestionsHash,
        dependencies: SmartSearchSuggestionsFamily._dependencies,
        allTransitiveDependencies:
            SmartSearchSuggestionsFamily._allTransitiveDependencies,
        query: query,
      );

  SmartSearchSuggestionsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final String query;

  @override
  Override overrideWith(
    FutureOr<List<SearchSuggestion>> Function(
      SmartSearchSuggestionsRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SmartSearchSuggestionsProvider._internal(
        (ref) => create(ref as SmartSearchSuggestionsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<SearchSuggestion>> createElement() {
    return _SmartSearchSuggestionsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SmartSearchSuggestionsProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SmartSearchSuggestionsRef
    on AutoDisposeFutureProviderRef<List<SearchSuggestion>> {
  /// The parameter `query` of this provider.
  String get query;
}

class _SmartSearchSuggestionsProviderElement
    extends AutoDisposeFutureProviderElement<List<SearchSuggestion>>
    with SmartSearchSuggestionsRef {
  _SmartSearchSuggestionsProviderElement(super.provider);

  @override
  String get query => (origin as SmartSearchSuggestionsProvider).query;
}

String _$popularSearchesHash() => r'fb7c2deb786a431c243292380a151f6fe483cd25';

/// Provider for popular searches
///
/// Copied from [popularSearches].
@ProviderFor(popularSearches)
final popularSearchesProvider =
    AutoDisposeFutureProvider<List<String>>.internal(
      popularSearches,
      name: r'popularSearchesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$popularSearchesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PopularSearchesRef = AutoDisposeFutureProviderRef<List<String>>;
String _$userSearchHistoryHash() => r'3f15f91d106f2c57d4d12f9c3b7d1964f7d992fb';

/// Provider for user's search history
///
/// Copied from [userSearchHistory].
@ProviderFor(userSearchHistory)
final userSearchHistoryProvider =
    AutoDisposeFutureProvider<List<String>>.internal(
      userSearchHistory,
      name: r'userSearchHistoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$userSearchHistoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserSearchHistoryRef = AutoDisposeFutureProviderRef<List<String>>;
String _$savedSearchesHash() => r'9d3e6a52a14208b2860ea1a86d61172499fb64e6';

/// Provider for saved searches
///
/// Copied from [savedSearches].
@ProviderFor(savedSearches)
final savedSearchesProvider = AutoDisposeFutureProvider<List<String>>.internal(
  savedSearches,
  name: r'savedSearchesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$savedSearchesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SavedSearchesRef = AutoDisposeFutureProviderRef<List<String>>;
String _$searchManagerHash() => r'c0475f11a227f66bc556571db65a7519084ec7ca';

/// Provider for search manager
///
/// Copied from [searchManager].
@ProviderFor(searchManager)
final searchManagerProvider = AutoDisposeProvider<SearchManager>.internal(
  searchManager,
  name: r'searchManagerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$searchManagerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SearchManagerRef = AutoDisposeProviderRef<SearchManager>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
