// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SellerApplicationModel _$SellerApplicationModelFromJson(
  Map<String, dynamic> json,
) => _SellerApplicationModel(
  id: json['id'] as String?,
  userId: json['user_id'] as String,
  storeName: json['store_name'] as String,
  phoneNumber: json['contact_phone'] as String,
  businessDescription: json['business_description'] as String?,
  sellerType: $enumDecode(_$SellerTypeEnumMap, json['seller_type']),
  companyName: json['company_name'] as String?,
  businessLicenseNumber: json['company_registration_id'] as String?,
  taxId: json['company_tax_id'] as String?,
  companyAddress: json['company_address'] as String?,
  website: json['company_website'] as String?,
  status:
      $enumDecodeNullable(_$ApplicationStatusEnumMap, json['status']) ??
      ApplicationStatus.pending,
  adminNotes: json['admin_notes'] as String?,
  reviewedBy: json['reviewed_by'] as String?,
  reviewedAt: json['reviewed_at'] == null
      ? null
      : DateTime.parse(json['reviewed_at'] as String),
  submittedAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
  isDeleted: json['is_deleted'] as bool? ?? false,
);

Map<String, dynamic> _$SellerApplicationModelToJson(
  _SellerApplicationModel instance,
) => <String, dynamic>{
  'user_id': instance.userId,
  'store_name': instance.storeName,
  'contact_phone': instance.phoneNumber,
  'business_description': instance.businessDescription,
  'seller_type': _$SellerTypeEnumMap[instance.sellerType]!,
  'company_name': instance.companyName,
  'company_registration_id': instance.businessLicenseNumber,
  'company_tax_id': instance.taxId,
  'company_address': instance.companyAddress,
  'company_website': instance.website,
  'status': _$ApplicationStatusEnumMap[instance.status]!,
  'admin_notes': instance.adminNotes,
  'reviewed_by': instance.reviewedBy,
  'reviewed_at': instance.reviewedAt?.toIso8601String(),
};

const _$SellerTypeEnumMap = {
  SellerType.individual: 'individual',
  SellerType.premium: 'premium',
  SellerType.business: 'business',
};

const _$ApplicationStatusEnumMap = {
  ApplicationStatus.pending: 'pending',
  ApplicationStatus.underReview: 'under_review',
  ApplicationStatus.approved: 'approved',
  ApplicationStatus.rejected: 'rejected',
  ApplicationStatus.needsMoreInfo: 'needs_more_info',
};
