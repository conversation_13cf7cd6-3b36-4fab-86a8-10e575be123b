// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_VehicleModel _$VehicleModelFromJson(Map<String, dynamic> json) =>
    _VehicleModel(
      id: (json['id'] as num).toInt(),
      makeId: (json['make_id'] as num).toInt(),
      name: json['name'] as String,
      generation: json['generation'] as String?,
      bodyType: json['body_type'] as String?,
      fuelType: json['fuel_type'] as String?,
      yearStart: (json['year_start'] as num).toInt(),
      yearEnd: (json['year_end'] as num?)?.toInt(),
      isCurrent: json['is_current'] as bool? ?? true,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      isDeleted: json['is_deleted'] as bool? ?? false,
    );

Map<String, dynamic> _$VehicleModelToJson(_VehicleModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'make_id': instance.makeId,
      'name': instance.name,
      'generation': instance.generation,
      'body_type': instance.bodyType,
      'fuel_type': instance.fuelType,
      'year_start': instance.yearStart,
      'year_end': instance.yearEnd,
      'is_current': instance.isCurrent,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'is_deleted': instance.isDeleted,
    };
