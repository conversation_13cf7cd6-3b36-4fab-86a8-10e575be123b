// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WalletModel _$WalletModelFromJson(Map<String, dynamic> json) => _WalletModel(
  id: json['id'] as String,
  userId: json['user_id'] as String,
  balance: (json['balance'] as num?)?.toDouble() ?? 0.0,
  frozenBalance: (json['frozen_balance'] as num?)?.toDouble() ?? 0.0,
  currency: json['currency'] as String? ?? 'LYD',
  dailyLimit: (json['daily_limit'] as num?)?.toDouble() ?? 1000.0,
  monthlyLimit: (json['monthly_limit'] as num?)?.toDouble() ?? 10000.0,
  status: json['status'] as String? ?? 'active',
  isActive: json['is_active'] as bool? ?? true,
  lastResetDaily: json['last_reset_daily'] == null
      ? null
      : DateTime.parse(json['last_reset_daily'] as String),
  lastResetMonthly: json['last_reset_monthly'] == null
      ? null
      : DateTime.parse(json['last_reset_monthly'] as String),
  createdAt: DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
  isDeleted: json['is_deleted'] as bool? ?? false,
  verificationLevel: json['verification_level'] as String? ?? 'unverified',
  minWithdrawal: (json['min_withdrawal'] as num?)?.toDouble() ?? 10.0,
);

Map<String, dynamic> _$WalletModelToJson(_WalletModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'balance': instance.balance,
      'frozen_balance': instance.frozenBalance,
      'currency': instance.currency,
      'daily_limit': instance.dailyLimit,
      'monthly_limit': instance.monthlyLimit,
      'status': instance.status,
      'is_active': instance.isActive,
      'last_reset_daily': instance.lastResetDaily?.toIso8601String(),
      'last_reset_monthly': instance.lastResetMonthly?.toIso8601String(),
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'is_deleted': instance.isDeleted,
      'verification_level': instance.verificationLevel,
      'min_withdrawal': instance.minWithdrawal,
    };
