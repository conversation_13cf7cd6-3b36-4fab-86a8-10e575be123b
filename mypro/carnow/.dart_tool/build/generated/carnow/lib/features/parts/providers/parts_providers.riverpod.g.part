// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$completePartHash() => r'0177a8b586345dd60479905578f25a3de08a56a2';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// مقدم القطعة الكاملة (قطعة + توافق + خصائص)
/// Complete Part Provider (part + compatibility + attributes)
///
/// Copied from [completePart].
@ProviderFor(completePart)
const completePartProvider = CompletePartFamily();

/// مقدم القطعة الكاملة (قطعة + توافق + خصائص)
/// Complete Part Provider (part + compatibility + attributes)
///
/// Copied from [completePart].
class CompletePartFamily extends Family<AsyncValue<CompletePartModel?>> {
  /// مقدم القطعة الكاملة (قطعة + توافق + خصائص)
  /// Complete Part Provider (part + compatibility + attributes)
  ///
  /// Copied from [completePart].
  const CompletePartFamily();

  /// مقدم القطعة الكاملة (قطعة + توافق + خصائص)
  /// Complete Part Provider (part + compatibility + attributes)
  ///
  /// Copied from [completePart].
  CompletePartProvider call(String partId) {
    return CompletePartProvider(partId);
  }

  @override
  CompletePartProvider getProviderOverride(
    covariant CompletePartProvider provider,
  ) {
    return call(provider.partId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'completePartProvider';
}

/// مقدم القطعة الكاملة (قطعة + توافق + خصائص)
/// Complete Part Provider (part + compatibility + attributes)
///
/// Copied from [completePart].
class CompletePartProvider
    extends AutoDisposeFutureProvider<CompletePartModel?> {
  /// مقدم القطعة الكاملة (قطعة + توافق + خصائص)
  /// Complete Part Provider (part + compatibility + attributes)
  ///
  /// Copied from [completePart].
  CompletePartProvider(String partId)
    : this._internal(
        (ref) => completePart(ref as CompletePartRef, partId),
        from: completePartProvider,
        name: r'completePartProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$completePartHash,
        dependencies: CompletePartFamily._dependencies,
        allTransitiveDependencies:
            CompletePartFamily._allTransitiveDependencies,
        partId: partId,
      );

  CompletePartProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.partId,
  }) : super.internal();

  final String partId;

  @override
  Override overrideWith(
    FutureOr<CompletePartModel?> Function(CompletePartRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CompletePartProvider._internal(
        (ref) => create(ref as CompletePartRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        partId: partId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<CompletePartModel?> createElement() {
    return _CompletePartProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CompletePartProvider && other.partId == partId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, partId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CompletePartRef on AutoDisposeFutureProviderRef<CompletePartModel?> {
  /// The parameter `partId` of this provider.
  String get partId;
}

class _CompletePartProviderElement
    extends AutoDisposeFutureProviderElement<CompletePartModel?>
    with CompletePartRef {
  _CompletePartProviderElement(super.provider);

  @override
  String get partId => (origin as CompletePartProvider).partId;
}

String _$partsHash() => r'5b4cdb7798ad87ac9343eecb4297677a261791bc';

/// مقدم القطع
/// Parts Provider
///
/// Copied from [Parts].
@ProviderFor(Parts)
final partsProvider =
    AutoDisposeAsyncNotifierProvider<Parts, List<PartModel>>.internal(
      Parts.new,
      name: r'partsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$partsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$Parts = AutoDisposeAsyncNotifier<List<PartModel>>;
String _$partCompatibilityHash() => r'b65ec2cc32322130a69aebe13dcd6b8e2ca53fbb';

/// مقدم توافق القطع
/// Part Compatibility Provider
///
/// Copied from [PartCompatibility].
@ProviderFor(PartCompatibility)
final partCompatibilityProvider =
    AutoDisposeAsyncNotifierProvider<
      PartCompatibility,
      List<PartCompatibilityModel>
    >.internal(
      PartCompatibility.new,
      name: r'partCompatibilityProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$partCompatibilityHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PartCompatibility =
    AutoDisposeAsyncNotifier<List<PartCompatibilityModel>>;
String _$partAttributesHash() => r'5ab0f4b0be3aa627a46a91943a5e8452eb045a01';

/// مقدم خصائص القطع
/// Part Attributes Provider
///
/// Copied from [PartAttributes].
@ProviderFor(PartAttributes)
final partAttributesProvider =
    AutoDisposeAsyncNotifierProvider<
      PartAttributes,
      List<PartAttributeModel>
    >.internal(
      PartAttributes.new,
      name: r'partAttributesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$partAttributesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PartAttributes = AutoDisposeAsyncNotifier<List<PartAttributeModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
