// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$warrantyRepositoryHash() =>
    r'86ae5303e9bb317ef52352eef99f2db482c5bf82';

/// See also [warrantyRepository].
@ProviderFor(warrantyRepository)
final warrantyRepositoryProvider =
    AutoDisposeProvider<WarrantyRepository>.internal(
      warrantyRepository,
      name: r'warrantyRepositoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$warrantyRepositoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WarrantyRepositoryRef = AutoDisposeProviderRef<WarrantyRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
