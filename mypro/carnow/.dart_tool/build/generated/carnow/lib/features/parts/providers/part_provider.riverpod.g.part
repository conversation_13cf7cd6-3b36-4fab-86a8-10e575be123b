// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$allPartsHash() => r'69bd3586b5576058ad2b94483e8013c65c99fb02';

/// مزود جميع قطع الغيار
///
/// Copied from [allParts].
@ProviderFor(allParts)
final allPartsProvider = AutoDisposeFutureProvider<List<ProductModel>>.internal(
  allParts,
  name: r'allPartsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$allPartsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AllPartsRef = AutoDisposeFutureProviderRef<List<ProductModel>>;
String _$partProviderHash() => r'7d7ccee475682c4e8175de42746374eae62a338a';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// مزود قطعة غيار واحدة
///
/// Copied from [partProvider].
@ProviderFor(partProvider)
const partProviderProvider = PartProviderFamily();

/// مزود قطعة غيار واحدة
///
/// Copied from [partProvider].
class PartProviderFamily extends Family<AsyncValue<ProductModel?>> {
  /// مزود قطعة غيار واحدة
  ///
  /// Copied from [partProvider].
  const PartProviderFamily();

  /// مزود قطعة غيار واحدة
  ///
  /// Copied from [partProvider].
  PartProviderProvider call(String partId) {
    return PartProviderProvider(partId);
  }

  @override
  PartProviderProvider getProviderOverride(
    covariant PartProviderProvider provider,
  ) {
    return call(provider.partId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'partProviderProvider';
}

/// مزود قطعة غيار واحدة
///
/// Copied from [partProvider].
class PartProviderProvider extends AutoDisposeFutureProvider<ProductModel?> {
  /// مزود قطعة غيار واحدة
  ///
  /// Copied from [partProvider].
  PartProviderProvider(String partId)
    : this._internal(
        (ref) => partProvider(ref as PartProviderRef, partId),
        from: partProviderProvider,
        name: r'partProviderProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$partProviderHash,
        dependencies: PartProviderFamily._dependencies,
        allTransitiveDependencies:
            PartProviderFamily._allTransitiveDependencies,
        partId: partId,
      );

  PartProviderProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.partId,
  }) : super.internal();

  final String partId;

  @override
  Override overrideWith(
    FutureOr<ProductModel?> Function(PartProviderRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: PartProviderProvider._internal(
        (ref) => create(ref as PartProviderRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        partId: partId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ProductModel?> createElement() {
    return _PartProviderProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is PartProviderProvider && other.partId == partId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, partId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin PartProviderRef on AutoDisposeFutureProviderRef<ProductModel?> {
  /// The parameter `partId` of this provider.
  String get partId;
}

class _PartProviderProviderElement
    extends AutoDisposeFutureProviderElement<ProductModel?>
    with PartProviderRef {
  _PartProviderProviderElement(super.provider);

  @override
  String get partId => (origin as PartProviderProvider).partId;
}

String _$partsByCategoryHash() => r'9c04d2b35ac737cc0fc1d1f83e1bdce64457888f';

/// مزود قطع الغيار حسب الفئة
///
/// Copied from [partsByCategory].
@ProviderFor(partsByCategory)
const partsByCategoryProvider = PartsByCategoryFamily();

/// مزود قطع الغيار حسب الفئة
///
/// Copied from [partsByCategory].
class PartsByCategoryFamily extends Family<AsyncValue<List<ProductModel>>> {
  /// مزود قطع الغيار حسب الفئة
  ///
  /// Copied from [partsByCategory].
  const PartsByCategoryFamily();

  /// مزود قطع الغيار حسب الفئة
  ///
  /// Copied from [partsByCategory].
  PartsByCategoryProvider call(String categoryId) {
    return PartsByCategoryProvider(categoryId);
  }

  @override
  PartsByCategoryProvider getProviderOverride(
    covariant PartsByCategoryProvider provider,
  ) {
    return call(provider.categoryId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'partsByCategoryProvider';
}

/// مزود قطع الغيار حسب الفئة
///
/// Copied from [partsByCategory].
class PartsByCategoryProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// مزود قطع الغيار حسب الفئة
  ///
  /// Copied from [partsByCategory].
  PartsByCategoryProvider(String categoryId)
    : this._internal(
        (ref) => partsByCategory(ref as PartsByCategoryRef, categoryId),
        from: partsByCategoryProvider,
        name: r'partsByCategoryProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$partsByCategoryHash,
        dependencies: PartsByCategoryFamily._dependencies,
        allTransitiveDependencies:
            PartsByCategoryFamily._allTransitiveDependencies,
        categoryId: categoryId,
      );

  PartsByCategoryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categoryId,
  }) : super.internal();

  final String categoryId;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(PartsByCategoryRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: PartsByCategoryProvider._internal(
        (ref) => create(ref as PartsByCategoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categoryId: categoryId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _PartsByCategoryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is PartsByCategoryProvider && other.categoryId == categoryId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categoryId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin PartsByCategoryRef on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `categoryId` of this provider.
  String get categoryId;
}

class _PartsByCategoryProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with PartsByCategoryRef {
  _PartsByCategoryProviderElement(super.provider);

  @override
  String get categoryId => (origin as PartsByCategoryProvider).categoryId;
}

String _$searchPartsHash() => r'0a88b3c9cfb42ece50fbf0e46770018d4627eaee';

/// مزود البحث في قطع الغيار
///
/// Copied from [searchParts].
@ProviderFor(searchParts)
const searchPartsProvider = SearchPartsFamily();

/// مزود البحث في قطع الغيار
///
/// Copied from [searchParts].
class SearchPartsFamily extends Family<AsyncValue<List<ProductModel>>> {
  /// مزود البحث في قطع الغيار
  ///
  /// Copied from [searchParts].
  const SearchPartsFamily();

  /// مزود البحث في قطع الغيار
  ///
  /// Copied from [searchParts].
  SearchPartsProvider call(String query) {
    return SearchPartsProvider(query);
  }

  @override
  SearchPartsProvider getProviderOverride(
    covariant SearchPartsProvider provider,
  ) {
    return call(provider.query);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'searchPartsProvider';
}

/// مزود البحث في قطع الغيار
///
/// Copied from [searchParts].
class SearchPartsProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// مزود البحث في قطع الغيار
  ///
  /// Copied from [searchParts].
  SearchPartsProvider(String query)
    : this._internal(
        (ref) => searchParts(ref as SearchPartsRef, query),
        from: searchPartsProvider,
        name: r'searchPartsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$searchPartsHash,
        dependencies: SearchPartsFamily._dependencies,
        allTransitiveDependencies: SearchPartsFamily._allTransitiveDependencies,
        query: query,
      );

  SearchPartsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final String query;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(SearchPartsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SearchPartsProvider._internal(
        (ref) => create(ref as SearchPartsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _SearchPartsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SearchPartsProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SearchPartsRef on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `query` of this provider.
  String get query;
}

class _SearchPartsProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with SearchPartsRef {
  _SearchPartsProviderElement(super.provider);

  @override
  String get query => (origin as SearchPartsProvider).query;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
