// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SmartSuggestion _$SmartSuggestionFromJson(Map<String, dynamic> json) =>
    _SmartSuggestion(
      title: json['title'] as String,
      description: json['description'] as String,
      filters: AdvancedSearchFilters.fromJson(
        json['filters'] as Map<String, dynamic>,
      ),
      estimatedResults: (json['estimatedResults'] as num?)?.toInt() ?? 0,
      confidence: (json['confidence'] as num?)?.toDouble() ?? 0,
      icon: json['icon'] as String?,
      category: json['category'] as String?,
    );

Map<String, dynamic> _$SmartSuggestionToJson(_SmartSuggestion instance) =>
    <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
      'filters': instance.filters,
      'estimatedResults': instance.estimatedResults,
      'confidence': instance.confidence,
      'icon': instance.icon,
      'category': instance.category,
    };
