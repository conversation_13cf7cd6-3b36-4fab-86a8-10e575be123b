// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$cartRepositoryHash() => r'e4ecd28533d8d6e21f3d16cad20e5cb6a02a9d23';

/// Enhanced Cart Repository Provider
///
/// Copied from [cartRepository].
@ProviderFor(cartRepository)
final cartRepositoryProvider = AutoDisposeProvider<CartRepository>.internal(
  cartRepository,
  name: r'cartRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$cartRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CartRepositoryRef = AutoDisposeProviderRef<CartRepository>;
String _$cartSummaryHash() => r'496ea8c66f4ee45c8ce771ed201c0bb85388479c';

/// Cart Summary Provider for quick access
///
/// Copied from [cartSummary].
@ProviderFor(cartSummary)
final cartSummaryProvider = AutoDisposeProvider<CartSummaryModel>.internal(
  cartSummary,
  name: r'cartSummaryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$cartSummaryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CartSummaryRef = AutoDisposeProviderRef<CartSummaryModel>;
String _$cartItemCountHash() => r'1cebe67c205bc583ba2a34f9cb2e401ef9dec3fa';

/// Cart Item Count Provider for badges and quick display
///
/// Copied from [cartItemCount].
@ProviderFor(cartItemCount)
final cartItemCountProvider = AutoDisposeProvider<int>.internal(
  cartItemCount,
  name: r'cartItemCountProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$cartItemCountHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CartItemCountRef = AutoDisposeProviderRef<int>;
String _$cartTotalHash() => r'bb93a39cda984244e106f73bc4b67b905c25d11d';

/// Cart Total Provider for quick display
///
/// Copied from [cartTotal].
@ProviderFor(cartTotal)
final cartTotalProvider = AutoDisposeProvider<double>.internal(
  cartTotal,
  name: r'cartTotalProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$cartTotalHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CartTotalRef = AutoDisposeProviderRef<double>;
String _$isCartEmptyHash() => r'55479818ed4202c183a28885da75a8ccc7ac867c';

/// Cart Empty State Provider
///
/// Copied from [isCartEmpty].
@ProviderFor(isCartEmpty)
final isCartEmptyProvider = AutoDisposeProvider<bool>.internal(
  isCartEmpty,
  name: r'isCartEmptyProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isCartEmptyHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsCartEmptyRef = AutoDisposeProviderRef<bool>;
String _$isCartLoadingHash() => r'800f29fbba085af06a19665a41dad42ba0806466';

/// Cart Loading State Provider
///
/// Copied from [isCartLoading].
@ProviderFor(isCartLoading)
final isCartLoadingProvider = AutoDisposeProvider<bool>.internal(
  isCartLoading,
  name: r'isCartLoadingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isCartLoadingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsCartLoadingRef = AutoDisposeProviderRef<bool>;
String _$enhancedCartHash() => r'd5e0af9a9483366cd3ebe02f96d65bc7f21cc073';

/// Enhanced Cart Provider following Forever Plan architecture
/// Manages the complete cart state with proper error handling and caching
///
/// Copied from [EnhancedCart].
@ProviderFor(EnhancedCart)
final enhancedCartProvider =
    AutoDisposeAsyncNotifierProvider<EnhancedCart, CartModel?>.internal(
      EnhancedCart.new,
      name: r'enhancedCartProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$enhancedCartHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$EnhancedCart = AutoDisposeAsyncNotifier<CartModel?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
