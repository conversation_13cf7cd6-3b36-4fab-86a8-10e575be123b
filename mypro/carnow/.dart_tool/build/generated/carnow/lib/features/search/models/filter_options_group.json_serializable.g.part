// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_FilterOptionsGroup _$FilterOptionsGroupFromJson(Map<String, dynamic> json) =>
    _FilterOptionsGroup(
      title: json['title'] as String,
      key: json['key'] as String,
      options: (json['options'] as List<dynamic>)
          .map((e) => FilterOption.fromJson(e as Map<String, dynamic>))
          .toList(),
      multiSelect: json['multiSelect'] as bool? ?? false,
      isExpanded: json['isExpanded'] as bool? ?? false,
      description: json['description'] as String?,
    );

Map<String, dynamic> _$FilterOptionsGroupToJson(_FilterOptionsGroup instance) =>
    <String, dynamic>{
      'title': instance.title,
      'key': instance.key,
      'options': instance.options,
      'multiSelect': instance.multiSelect,
      'isExpanded': instance.isExpanded,
      'description': instance.description,
    };
