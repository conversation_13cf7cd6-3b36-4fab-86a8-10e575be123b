// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$dashboardDataHash() => r'78d4c31f7ce2961070e954e7595044d7dcf2f4fa';

/// See also [DashboardData].
@ProviderFor(DashboardData)
final dashboardDataProvider =
    AutoDisposeNotifierProvider<DashboardData, Map<String, dynamic>>.internal(
      DashboardData.new,
      name: r'dashboardDataProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$dashboardDataHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$DashboardData = AutoDisposeNotifier<Map<String, dynamic>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
