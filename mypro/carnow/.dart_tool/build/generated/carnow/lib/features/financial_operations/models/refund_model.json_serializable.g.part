// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_RefundModel _$RefundModelFromJson(Map<String, dynamic> json) => _RefundModel(
  id: json['id'] as String,
  transactionId: json['transaction_id'] as String,
  orderId: json['order_id'] as String?,
  productId: json['product_id'] as String?,
  requesterId: json['requester_id'] as String,
  sellerId: json['seller_id'] as String,
  originalAmount: (json['original_amount'] as num?)?.toDouble() ?? 0.0,
  refundAmount: (json['refund_amount'] as num?)?.toDouble() ?? 0.0,
  refundFee: (json['refund_fee'] as num?)?.toDouble() ?? 0.0,
  netRefundAmount: (json['net_refund_amount'] as num?)?.toDouble() ?? 0.0,
  currency: json['currency'] as String? ?? 'LYD',
  refundType:
      $enumDecodeNullable(_$RefundTypeEnumMap, json['refund_type']) ??
      RefundType.manual,
  refundReason:
      $enumDecodeNullable(_$RefundReasonEnumMap, json['refund_reason']) ??
      RefundReason.other,
  status:
      $enumDecodeNullable(_$RefundStatusEnumMap, json['status']) ??
      RefundStatus.requested,
  reasonDescription: json['reason_description'] as String? ?? '',
  adminNotes: json['admin_notes'] as String?,
  rejectionReason: json['rejection_reason'] as String?,
  supportingDocuments:
      (json['supporting_documents'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  estimatedProcessingTime: (json['estimated_processing_time'] as num?)?.toInt(),
  createdAt: DateTime.parse(json['created_at'] as String),
  updatedAt: DateTime.parse(json['updated_at'] as String),
  approvedAt: json['approved_at'] == null
      ? null
      : DateTime.parse(json['approved_at'] as String),
  processedAt: json['processed_at'] == null
      ? null
      : DateTime.parse(json['processed_at'] as String),
  completedAt: json['completed_at'] == null
      ? null
      : DateTime.parse(json['completed_at'] as String),
  approvedBy: json['approved_by'] as String?,
  processedBy: json['processed_by'] as String?,
);

Map<String, dynamic> _$RefundModelToJson(_RefundModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'transaction_id': instance.transactionId,
      'order_id': instance.orderId,
      'product_id': instance.productId,
      'requester_id': instance.requesterId,
      'seller_id': instance.sellerId,
      'original_amount': instance.originalAmount,
      'refund_amount': instance.refundAmount,
      'refund_fee': instance.refundFee,
      'net_refund_amount': instance.netRefundAmount,
      'currency': instance.currency,
      'refund_type': _$RefundTypeEnumMap[instance.refundType]!,
      'refund_reason': _$RefundReasonEnumMap[instance.refundReason]!,
      'status': _$RefundStatusEnumMap[instance.status]!,
      'reason_description': instance.reasonDescription,
      'admin_notes': instance.adminNotes,
      'rejection_reason': instance.rejectionReason,
      'supporting_documents': instance.supportingDocuments,
      'estimated_processing_time': instance.estimatedProcessingTime,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'approved_at': instance.approvedAt?.toIso8601String(),
      'processed_at': instance.processedAt?.toIso8601String(),
      'completed_at': instance.completedAt?.toIso8601String(),
      'approved_by': instance.approvedBy,
      'processed_by': instance.processedBy,
    };

const _$RefundTypeEnumMap = {
  RefundType.full: 'full',
  RefundType.partial: 'partial',
  RefundType.automatic: 'automatic',
  RefundType.manual: 'manual',
};

const _$RefundReasonEnumMap = {
  RefundReason.defectiveProduct: 'defective_product',
  RefundReason.wrongProduct: 'wrong_product',
  RefundReason.productNotAsDescribed: 'product_not_as_described',
  RefundReason.deliveryIssue: 'delivery_issue',
  RefundReason.customerChangedMind: 'customer_changed_mind',
  RefundReason.duplicateOrder: 'duplicate_order',
  RefundReason.sellerCancelled: 'seller_cancelled',
  RefundReason.paymentIssue: 'payment_issue',
  RefundReason.fraudDetected: 'fraud_detected',
  RefundReason.other: 'other',
};

const _$RefundStatusEnumMap = {
  RefundStatus.requested: 'requested',
  RefundStatus.pendingReview: 'pending_review',
  RefundStatus.approved: 'approved',
  RefundStatus.rejected: 'rejected',
  RefundStatus.processing: 'processing',
  RefundStatus.completed: 'completed',
  RefundStatus.failed: 'failed',
  RefundStatus.cancelled: 'cancelled',
};
