// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PartCategoryModel _$PartCategoryModelFromJson(Map<String, dynamic> json) =>
    _PartCategoryModel(
      id: json['id'] as String,
      subcategoryId: json['subcategory_id'] as String,
      nameAr: json['name_ar'] as String,
      nameEn: json['name_en'] as String,
      specificationSchema:
          json['specification_schema'] as Map<String, dynamic>?,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$PartCategoryModelToJson(_PartCategoryModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'subcategory_id': instance.subcategoryId,
      'name_ar': instance.nameAr,
      'name_en': instance.nameEn,
      'specification_schema': instance.specificationSchema,
      'created_at': instance.createdAt?.toIso8601String(),
    };
