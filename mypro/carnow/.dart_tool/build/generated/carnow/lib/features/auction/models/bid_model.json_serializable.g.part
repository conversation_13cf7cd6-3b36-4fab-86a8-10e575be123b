// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_BidModel _$BidModelFromJson(Map<String, dynamic> json) => _BidModel(
  id: (json['id'] as num?)?.toInt(),
  partId: (json['part_id'] as num?)?.toInt(),
  userId: json['user_id'] as String?,
  amount: (json['amount'] as num?)?.toDouble(),
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
  isDeleted: json['is_deleted'] as bool? ?? false,
);

Map<String, dynamic> _$BidModelToJson(_BidModel instance) => <String, dynamic>{
  'id': instance.id,
  'part_id': instance.partId,
  'user_id': instance.userId,
  'amount': instance.amount,
  'created_at': instance.createdAt?.toIso8601String(),
  'updated_at': instance.updatedAt?.toIso8601String(),
  'is_deleted': instance.isDeleted,
};
