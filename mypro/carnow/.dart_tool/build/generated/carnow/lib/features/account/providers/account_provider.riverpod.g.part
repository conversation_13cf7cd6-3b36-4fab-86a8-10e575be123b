// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$accountNotifierHash() => r'39824996841ab20cede0cd01e7fc0841ba780c22';

/// مزود لإدارة بيانات الحساب وملف المستخدم الشخصي - CLEAN: Uses HTTP API only
///
/// Copied from [AccountNotifier].
@ProviderFor(AccountNotifier)
final accountNotifierProvider =
    AutoDisposeAsyncNotifierProvider<AccountNotifier, UserModel?>.internal(
      AccountNotifier.new,
      name: r'accountNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$accountNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AccountNotifier = AutoDisposeAsyncNotifier<UserModel?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
