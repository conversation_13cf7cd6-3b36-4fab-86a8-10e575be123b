// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$supportTicketActionsHash() =>
    r'0c4436a609961984fa1dbf3d7e439aa8f8910721';

/// Support ticket actions provider - Clean implementation using Go backend
/// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Copied from [SupportTicketActions].
@ProviderFor(SupportTicketActions)
final supportTicketActionsProvider =
    AutoDisposeAsyncNotifierProvider<SupportTicketActions, void>.internal(
      SupportTicketActions.new,
      name: r'supportTicketActionsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$supportTicketActionsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SupportTicketActions = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
