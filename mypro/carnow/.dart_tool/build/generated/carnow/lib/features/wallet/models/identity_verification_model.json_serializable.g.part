// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_IdentityVerificationModel _$IdentityVerificationModelFromJson(
  Map<String, dynamic> json,
) => _IdentityVerificationModel(
  id: json['id'] as String,
  userId: json['userId'] as String,
  statusId: json['statusId'] as String,
  documentTypeId: json['documentTypeId'] as String?,
  documentNumber: json['documentNumber'] as String?,
  documentExpiry: json['documentExpiry'] == null
      ? null
      : DateTime.parse(json['documentExpiry'] as String),
  documentImageUrl: json['documentImageUrl'] as String?,
  verificationMethod: json['verificationMethod'] as String? ?? 'manual',
  verifiedAt: json['verifiedAt'] == null
      ? null
      : DateTime.parse(json['verifiedAt'] as String),
  verifiedBy: json['verifiedBy'] as String?,
  rejectionReason: json['rejectionReason'] as String?,
  notes: json['notes'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$IdentityVerificationModelToJson(
  _IdentityVerificationModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'statusId': instance.statusId,
  'documentTypeId': instance.documentTypeId,
  'documentNumber': instance.documentNumber,
  'documentExpiry': instance.documentExpiry?.toIso8601String(),
  'documentImageUrl': instance.documentImageUrl,
  'verificationMethod': instance.verificationMethod,
  'verifiedAt': instance.verifiedAt?.toIso8601String(),
  'verifiedBy': instance.verifiedBy,
  'rejectionReason': instance.rejectionReason,
  'notes': instance.notes,
  'metadata': instance.metadata,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};

_DocumentTypeModel _$DocumentTypeModelFromJson(Map<String, dynamic> json) =>
    _DocumentTypeModel(
      id: json['id'] as String,
      name: json['name'] as String,
      nameAr: json['nameAr'] as String,
      description: json['description'] as String?,
      isRequired: json['isRequired'] as bool? ?? true,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$DocumentTypeModelToJson(_DocumentTypeModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'nameAr': instance.nameAr,
      'description': instance.description,
      'isRequired': instance.isRequired,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

_VerificationStatusModel _$VerificationStatusModelFromJson(
  Map<String, dynamic> json,
) => _VerificationStatusModel(
  id: json['id'] as String,
  name: json['name'] as String,
  nameAr: json['nameAr'] as String,
  description: json['description'] as String?,
  level: (json['level'] as num?)?.toInt() ?? 0,
  createdAt: DateTime.parse(json['createdAt'] as String),
);

Map<String, dynamic> _$VerificationStatusModelToJson(
  _VerificationStatusModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'nameAr': instance.nameAr,
  'description': instance.description,
  'level': instance.level,
  'createdAt': instance.createdAt.toIso8601String(),
};

_WithdrawalRequestModel _$WithdrawalRequestModelFromJson(
  Map<String, dynamic> json,
) => _WithdrawalRequestModel(
  id: json['id'] as String,
  walletId: json['walletId'] as String,
  transactionId: json['transactionId'] as String?,
  amount: (json['amount'] as num).toDouble(),
  status:
      $enumDecodeNullable(_$WithdrawalRequestStatusEnumMap, json['status']) ??
      WithdrawalRequestStatus.pending,
  bankName: json['bankName'] as String?,
  accountNumber: json['accountNumber'] as String?,
  accountHolderName: json['accountHolderName'] as String?,
  iban: json['iban'] as String?,
  swiftCode: json['swiftCode'] as String?,
  processedAt: json['processedAt'] == null
      ? null
      : DateTime.parse(json['processedAt'] as String),
  processedBy: json['processedBy'] as String?,
  rejectionReason: json['rejectionReason'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$WithdrawalRequestModelToJson(
  _WithdrawalRequestModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'walletId': instance.walletId,
  'transactionId': instance.transactionId,
  'amount': instance.amount,
  'status': _$WithdrawalRequestStatusEnumMap[instance.status]!,
  'bankName': instance.bankName,
  'accountNumber': instance.accountNumber,
  'accountHolderName': instance.accountHolderName,
  'iban': instance.iban,
  'swiftCode': instance.swiftCode,
  'processedAt': instance.processedAt?.toIso8601String(),
  'processedBy': instance.processedBy,
  'rejectionReason': instance.rejectionReason,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};

const _$WithdrawalRequestStatusEnumMap = {
  WithdrawalRequestStatus.pending: 'pending',
  WithdrawalRequestStatus.approved: 'approved',
  WithdrawalRequestStatus.rejected: 'rejected',
  WithdrawalRequestStatus.processed: 'processed',
};
