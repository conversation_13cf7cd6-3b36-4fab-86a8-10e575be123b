// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$optimizedWalletsNotifierHash() =>
    r'6222ec397768ab15286f1405ed5d58e2f926df76';

/// ================================================
/// OPTIMIZED WALLET PROVIDERS مع التخزين المؤقت
/// ================================================
/// Provider محسن للمحافظ مع pagination و caching
///
/// Copied from [OptimizedWalletsNotifier].
@ProviderFor(OptimizedWalletsNotifier)
final optimizedWalletsNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      OptimizedWalletsNotifier,
      WalletListState
    >.internal(
      OptimizedWalletsNotifier.new,
      name: r'optimizedWalletsNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$optimizedWalletsNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$OptimizedWalletsNotifier = AutoDisposeAsyncNotifier<WalletListState>;
String _$optimizedWalletDetailsNotifierHash() =>
    r'8125e43e244d657498e72d36bbbb6e20c5410ac3';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$OptimizedWalletDetailsNotifier
    extends BuildlessAutoDisposeAsyncNotifier<WalletOverview?> {
  late final String walletId;

  FutureOr<WalletOverview?> build(String walletId);
}

/// Provider محسن لتفاصيل المحفظة مع caching
///
/// Copied from [OptimizedWalletDetailsNotifier].
@ProviderFor(OptimizedWalletDetailsNotifier)
const optimizedWalletDetailsNotifierProvider =
    OptimizedWalletDetailsNotifierFamily();

/// Provider محسن لتفاصيل المحفظة مع caching
///
/// Copied from [OptimizedWalletDetailsNotifier].
class OptimizedWalletDetailsNotifierFamily
    extends Family<AsyncValue<WalletOverview?>> {
  /// Provider محسن لتفاصيل المحفظة مع caching
  ///
  /// Copied from [OptimizedWalletDetailsNotifier].
  const OptimizedWalletDetailsNotifierFamily();

  /// Provider محسن لتفاصيل المحفظة مع caching
  ///
  /// Copied from [OptimizedWalletDetailsNotifier].
  OptimizedWalletDetailsNotifierProvider call(String walletId) {
    return OptimizedWalletDetailsNotifierProvider(walletId);
  }

  @override
  OptimizedWalletDetailsNotifierProvider getProviderOverride(
    covariant OptimizedWalletDetailsNotifierProvider provider,
  ) {
    return call(provider.walletId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'optimizedWalletDetailsNotifierProvider';
}

/// Provider محسن لتفاصيل المحفظة مع caching
///
/// Copied from [OptimizedWalletDetailsNotifier].
class OptimizedWalletDetailsNotifierProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          OptimizedWalletDetailsNotifier,
          WalletOverview?
        > {
  /// Provider محسن لتفاصيل المحفظة مع caching
  ///
  /// Copied from [OptimizedWalletDetailsNotifier].
  OptimizedWalletDetailsNotifierProvider(String walletId)
    : this._internal(
        () => OptimizedWalletDetailsNotifier()..walletId = walletId,
        from: optimizedWalletDetailsNotifierProvider,
        name: r'optimizedWalletDetailsNotifierProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$optimizedWalletDetailsNotifierHash,
        dependencies: OptimizedWalletDetailsNotifierFamily._dependencies,
        allTransitiveDependencies:
            OptimizedWalletDetailsNotifierFamily._allTransitiveDependencies,
        walletId: walletId,
      );

  OptimizedWalletDetailsNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.walletId,
  }) : super.internal();

  final String walletId;

  @override
  FutureOr<WalletOverview?> runNotifierBuild(
    covariant OptimizedWalletDetailsNotifier notifier,
  ) {
    return notifier.build(walletId);
  }

  @override
  Override overrideWith(OptimizedWalletDetailsNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: OptimizedWalletDetailsNotifierProvider._internal(
        () => create()..walletId = walletId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        walletId: walletId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<
    OptimizedWalletDetailsNotifier,
    WalletOverview?
  >
  createElement() {
    return _OptimizedWalletDetailsNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is OptimizedWalletDetailsNotifierProvider &&
        other.walletId == walletId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, walletId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin OptimizedWalletDetailsNotifierRef
    on AutoDisposeAsyncNotifierProviderRef<WalletOverview?> {
  /// The parameter `walletId` of this provider.
  String get walletId;
}

class _OptimizedWalletDetailsNotifierProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          OptimizedWalletDetailsNotifier,
          WalletOverview?
        >
    with OptimizedWalletDetailsNotifierRef {
  _OptimizedWalletDetailsNotifierProviderElement(super.provider);

  @override
  String get walletId =>
      (origin as OptimizedWalletDetailsNotifierProvider).walletId;
}

String _$optimizedSearchNotifierHash() =>
    r'f2cb4c3dc5f00159aca0e2f0f57e561729d015e6';

/// Provider محسن للبحث مع debouncing
///
/// Copied from [OptimizedSearchNotifier].
@ProviderFor(OptimizedSearchNotifier)
final optimizedSearchNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      OptimizedSearchNotifier,
      List<WalletOverview>
    >.internal(
      OptimizedSearchNotifier.new,
      name: r'optimizedSearchNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$optimizedSearchNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$OptimizedSearchNotifier =
    AutoDisposeAsyncNotifier<List<WalletOverview>>;
String _$walletFiltersNotifierHash() =>
    r'4e5131e2fc591e48dbbbf82e380ffcd76a9c3832';

/// Provider للفلاتر
///
/// Copied from [WalletFiltersNotifier].
@ProviderFor(WalletFiltersNotifier)
final walletFiltersNotifierProvider =
    AutoDisposeNotifierProvider<WalletFiltersNotifier, WalletFilters>.internal(
      WalletFiltersNotifier.new,
      name: r'walletFiltersNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$walletFiltersNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$WalletFiltersNotifier = AutoDisposeNotifier<WalletFilters>;
String _$debouncedSearchNotifierHash() =>
    r'462034b62fd411e823931d60868122c5528fb251';

/// Provider لـ debounced search
///
/// Copied from [DebouncedSearchNotifier].
@ProviderFor(DebouncedSearchNotifier)
final debouncedSearchNotifierProvider =
    AutoDisposeNotifierProvider<DebouncedSearchNotifier, String>.internal(
      DebouncedSearchNotifier.new,
      name: r'debouncedSearchNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$debouncedSearchNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$DebouncedSearchNotifier = AutoDisposeNotifier<String>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
