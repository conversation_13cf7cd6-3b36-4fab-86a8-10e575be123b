// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_VehicleSuggestion _$VehicleSuggestionFromJson(Map<String, dynamic> json) =>
    _VehicleSuggestion(
      type: json['type'] as String,
      name: json['name'] as String,
      display: json['display'] as String,
      subtitle: json['subtitle'] as String?,
      arabicName: json['arabicName'] as String?,
      make: json['make'] as String?,
      model: json['model'] as String?,
      value: (json['value'] as num?)?.toInt(),
      isSelected: json['isSelected'] as bool? ?? false,
      isLoading: json['isLoading'] as bool? ?? false,
    );

Map<String, dynamic> _$VehicleSuggestionToJson(_VehicleSuggestion instance) =>
    <String, dynamic>{
      'type': instance.type,
      'name': instance.name,
      'display': instance.display,
      'subtitle': instance.subtitle,
      'arabicName': instance.arabicName,
      'make': instance.make,
      'model': instance.model,
      'value': instance.value,
      'isSelected': instance.isSelected,
      'isLoading': instance.isLoading,
    };

_VehicleSearchState _$VehicleSearchStateFromJson(Map<String, dynamic> json) =>
    _VehicleSearchState(
      query: json['query'] as String? ?? '',
      suggestions:
          (json['suggestions'] as List<dynamic>?)
              ?.map(
                (e) => VehicleSuggestion.fromJson(e as Map<String, dynamic>),
              )
              .toList() ??
          const [],
      isLoading: json['isLoading'] as bool? ?? false,
      hasError: json['hasError'] as bool? ?? false,
      errorMessage: json['errorMessage'] as String?,
      selectedSuggestion: json['selectedSuggestion'] == null
          ? null
          : VehicleSuggestion.fromJson(
              json['selectedSuggestion'] as Map<String, dynamic>,
            ),
      currentStep: (json['currentStep'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$VehicleSearchStateToJson(_VehicleSearchState instance) =>
    <String, dynamic>{
      'query': instance.query,
      'suggestions': instance.suggestions,
      'isLoading': instance.isLoading,
      'hasError': instance.hasError,
      'errorMessage': instance.errorMessage,
      'selectedSuggestion': instance.selectedSuggestion,
      'currentStep': instance.currentStep,
    };

_SelectedVehicleDetails _$SelectedVehicleDetailsFromJson(
  Map<String, dynamic> json,
) => _SelectedVehicleDetails(
  year: (json['year'] as num).toInt(),
  make: json['make'] as String,
  model: json['model'] as String,
  trim: json['trim'] as String?,
  engine: json['engine'] as String?,
  imageUrl: json['imageUrl'] as String?,
  fullName: json['fullName'] as String?,
  subtitle: json['subtitle'] as String?,
  isConfirmed: json['isConfirmed'] as bool? ?? false,
  isSaving: json['isSaving'] as bool? ?? false,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$SelectedVehicleDetailsToJson(
  _SelectedVehicleDetails instance,
) => <String, dynamic>{
  'year': instance.year,
  'make': instance.make,
  'model': instance.model,
  'trim': instance.trim,
  'engine': instance.engine,
  'imageUrl': instance.imageUrl,
  'fullName': instance.fullName,
  'subtitle': instance.subtitle,
  'isConfirmed': instance.isConfirmed,
  'isSaving': instance.isSaving,
  'createdAt': instance.createdAt?.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
};

_VehicleType _$VehicleTypeFromJson(Map<String, dynamic> json) => _VehicleType(
  id: json['id'] as String,
  title: json['title'] as String,
  subtitle: json['subtitle'] as String,
  iconName: json['iconName'] as String,
  colorValue: (json['colorValue'] as num).toInt(),
  isSelected: json['isSelected'] as bool? ?? false,
);

Map<String, dynamic> _$VehicleTypeToJson(_VehicleType instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'subtitle': instance.subtitle,
      'iconName': instance.iconName,
      'colorValue': instance.colorValue,
      'isSelected': instance.isSelected,
    };

_VehicleSearchError _$VehicleSearchErrorFromJson(Map<String, dynamic> json) =>
    _VehicleSearchError(
      message: json['message'] as String,
      code: json['code'] as String,
      details: json['details'] as String?,
      timestamp: json['timestamp'] == null
          ? null
          : DateTime.parse(json['timestamp'] as String),
      isRetryable: json['isRetryable'] as bool? ?? false,
    );

Map<String, dynamic> _$VehicleSearchErrorToJson(_VehicleSearchError instance) =>
    <String, dynamic>{
      'message': instance.message,
      'code': instance.code,
      'details': instance.details,
      'timestamp': instance.timestamp?.toIso8601String(),
      'isRetryable': instance.isRetryable,
    };

_SearchPreferences _$SearchPreferencesFromJson(Map<String, dynamic> json) =>
    _SearchPreferences(
      maxResults: (json['maxResults'] as num?)?.toInt() ?? 20,
      debounceMs: (json['debounceMs'] as num?)?.toInt() ?? 300,
      enableCache: json['enableCache'] as bool? ?? true,
      enableArabicSearch: json['enableArabicSearch'] as bool? ?? true,
      searchTypes:
          (json['searchTypes'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const ['year', 'make', 'model'],
    );

Map<String, dynamic> _$SearchPreferencesToJson(_SearchPreferences instance) =>
    <String, dynamic>{
      'maxResults': instance.maxResults,
      'debounceMs': instance.debounceMs,
      'enableCache': instance.enableCache,
      'enableArabicSearch': instance.enableArabicSearch,
      'searchTypes': instance.searchTypes,
    };

_SearchAnalytics _$SearchAnalyticsFromJson(Map<String, dynamic> json) =>
    _SearchAnalytics(
      totalSearches: (json['totalSearches'] as num?)?.toInt() ?? 0,
      successfulSearches: (json['successfulSearches'] as num?)?.toInt() ?? 0,
      failedSearches: (json['failedSearches'] as num?)?.toInt() ?? 0,
      averageResponseTime:
          (json['averageResponseTime'] as num?)?.toDouble() ?? 0,
      popularQueries:
          (json['popularQueries'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      lastSearchAt: json['lastSearchAt'] == null
          ? null
          : DateTime.parse(json['lastSearchAt'] as String),
    );

Map<String, dynamic> _$SearchAnalyticsToJson(_SearchAnalytics instance) =>
    <String, dynamic>{
      'totalSearches': instance.totalSearches,
      'successfulSearches': instance.successfulSearches,
      'failedSearches': instance.failedSearches,
      'averageResponseTime': instance.averageResponseTime,
      'popularQueries': instance.popularQueries,
      'lastSearchAt': instance.lastSearchAt?.toIso8601String(),
    };
