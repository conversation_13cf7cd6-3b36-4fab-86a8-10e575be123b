// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userNotificationsHash() => r'213d5a86a997d14fc0d9faad32ea5cfb1066418e';

/// Provider for user notifications - Forever Plan Architecture
/// ✅ Uses NotificationApiService instead of direct Supabase calls
///
/// Copied from [userNotifications].
@ProviderFor(userNotifications)
final userNotificationsProvider =
    AutoDisposeFutureProvider<List<NotificationModel>>.internal(
      userNotifications,
      name: r'userNotificationsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$userNotificationsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserNotificationsRef =
    AutoDisposeFutureProviderRef<List<NotificationModel>>;
String _$unreadNotificationCountHash() =>
    r'c50c4bf05b9ff2acd783193f85133ef77e2793e6';

/// Provider for unread notification count
///
/// Copied from [unreadNotificationCount].
@ProviderFor(unreadNotificationCount)
final unreadNotificationCountProvider = AutoDisposeFutureProvider<int>.internal(
  unreadNotificationCount,
  name: r'unreadNotificationCountProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$unreadNotificationCountHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UnreadNotificationCountRef = AutoDisposeFutureProviderRef<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
