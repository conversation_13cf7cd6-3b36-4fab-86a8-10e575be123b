// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CarModel _$CarModelFromJson(Map<String, dynamic> json) => _CarModel(
  id: (json['id'] as num?)?.toInt(),
  userId: json['user_id'] as String?,
  makeId: (json['make_id'] as num?)?.toInt(),
  make: json['make'] as String?,
  modelId: (json['model_id'] as num?)?.toInt(),
  model: json['model'] as String?,
  year: (json['year'] as num?)?.toInt(),
  trimId: (json['trim_id'] as num?)?.toInt(),
  trim: json['trim'] as String?,
  engine: json['engine'] as String?,
  transmission: json['transmission'] as String?,
  fuelType: json['fuel_type'] as String?,
  mileage: (json['mileage'] as num?)?.toInt(),
  bodyType: json['body_type'] as String?,
  vin: json['vin'] as String?,
  imageUrl: json['image_url'] as String?,
  images:
      (json['images'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  colors:
      (json['colors'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  specifications: json['specifications'] as Map<String, dynamic>? ?? const {},
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
  isDeleted: json['is_deleted'] as bool? ?? false,
);

Map<String, dynamic> _$CarModelToJson(_CarModel instance) => <String, dynamic>{
  'id': instance.id,
  'user_id': instance.userId,
  'make_id': instance.makeId,
  'make': instance.make,
  'model_id': instance.modelId,
  'model': instance.model,
  'year': instance.year,
  'trim_id': instance.trimId,
  'trim': instance.trim,
  'engine': instance.engine,
  'transmission': instance.transmission,
  'fuel_type': instance.fuelType,
  'mileage': instance.mileage,
  'body_type': instance.bodyType,
  'vin': instance.vin,
  'image_url': instance.imageUrl,
  'images': instance.images,
  'colors': instance.colors,
  'specifications': instance.specifications,
  'created_at': instance.createdAt?.toIso8601String(),
  'updated_at': instance.updatedAt?.toIso8601String(),
  'is_deleted': instance.isDeleted,
};
