// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SellerStoreModel _$SellerStoreModelFromJson(Map<String, dynamic> json) =>
    _SellerStoreModel(
      id: (json['id'] as num?)?.toInt(),
      sellerId: (json['sellerId'] as num?)?.toInt(),
      name: json['name'] as String?,
      description: json['description'] as String?,
      logoUrl: json['logoUrl'] as String?,
      bannerUrl: json['bannerUrl'] as String?,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      address: json['address'] as String?,
      isVerified: json['isVerified'] as bool? ?? false,
      settings: json['settings'] as Map<String, dynamic>? ?? const {},
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$SellerStoreModelToJson(_SellerStoreModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sellerId': instance.sellerId,
      'name': instance.name,
      'description': instance.description,
      'logoUrl': instance.logoUrl,
      'bannerUrl': instance.bannerUrl,
      'phone': instance.phone,
      'email': instance.email,
      'address': instance.address,
      'isVerified': instance.isVerified,
      'settings': instance.settings,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };
