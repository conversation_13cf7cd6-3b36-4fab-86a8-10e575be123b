// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AdvancedSearchFilters _$AdvancedSearchFiltersFromJson(
  Map<String, dynamic> json,
) => _AdvancedSearchFilters(
  query: json['query'] as String?,
  category: json['category'] as String?,
  priceMin: (json['priceMin'] as num?)?.toDouble(),
  priceMax: (json['priceMax'] as num?)?.toDouble(),
  condition: json['condition'] as String?,
  listingType: json['listingType'] as String?,
  bodyStyleId: (json['body_style_id'] as num?)?.toInt(),
  transmissionTypeId: (json['transmission_type_id'] as num?)?.toInt(),
  brakeSystemId: (json['brake_system_id'] as num?)?.toInt(),
  valvetrainDesignId: (json['valvetrain_design_id'] as num?)?.toInt(),
  cabTypeId: (json['cab_type_id'] as num?)?.toInt(),
  bedTypeId: (json['bed_type_id'] as num?)?.toInt(),
  wheelbaseTypeId: (json['wheelbase_type_id'] as num?)?.toInt(),
  hasTurbo: json['has_turbo'] as bool?,
  blindSpotMonitoringId: (json['blind_spot_monitoring_id'] as num?)?.toInt(),
  blindSpotInterventionId: (json['blind_spot_intervention_id'] as num?)
      ?.toInt(),
  chargerLevelId: (json['charger_level_id'] as num?)?.toInt(),
  trailerTypeId: (json['trailer_type_id'] as num?)?.toInt(),
  make: json['make'] as String?,
  model: json['model'] as String?,
  yearFrom: (json['yearFrom'] as num?)?.toInt(),
  yearTo: (json['yearTo'] as num?)?.toInt(),
  engine: json['engine'] as String?,
  trim: json['trim'] as String?,
  location: json['location'] as String?,
  sellerId: json['sellerId'] as String?,
  verifiedSeller: json['verifiedSeller'] as bool?,
  minRating: (json['minRating'] as num?)?.toDouble(),
  minReviews: (json['minReviews'] as num?)?.toInt(),
  popularParts: json['popularParts'] as bool?,
  inStock: json['inStock'] as bool?,
  freeShipping: json['freeShipping'] as bool?,
  maxShippingDays: (json['maxShippingDays'] as num?)?.toInt(),
  sortBy: json['sortBy'] as String? ?? 'relevance',
  limit: (json['limit'] as num?)?.toInt() ?? 20,
  offset: (json['offset'] as num?)?.toInt() ?? 0,
);

Map<String, dynamic> _$AdvancedSearchFiltersToJson(
  _AdvancedSearchFilters instance,
) => <String, dynamic>{
  'query': instance.query,
  'category': instance.category,
  'priceMin': instance.priceMin,
  'priceMax': instance.priceMax,
  'condition': instance.condition,
  'listingType': instance.listingType,
  'body_style_id': instance.bodyStyleId,
  'transmission_type_id': instance.transmissionTypeId,
  'brake_system_id': instance.brakeSystemId,
  'valvetrain_design_id': instance.valvetrainDesignId,
  'cab_type_id': instance.cabTypeId,
  'bed_type_id': instance.bedTypeId,
  'wheelbase_type_id': instance.wheelbaseTypeId,
  'has_turbo': instance.hasTurbo,
  'blind_spot_monitoring_id': instance.blindSpotMonitoringId,
  'blind_spot_intervention_id': instance.blindSpotInterventionId,
  'charger_level_id': instance.chargerLevelId,
  'trailer_type_id': instance.trailerTypeId,
  'make': instance.make,
  'model': instance.model,
  'yearFrom': instance.yearFrom,
  'yearTo': instance.yearTo,
  'engine': instance.engine,
  'trim': instance.trim,
  'location': instance.location,
  'sellerId': instance.sellerId,
  'verifiedSeller': instance.verifiedSeller,
  'minRating': instance.minRating,
  'minReviews': instance.minReviews,
  'popularParts': instance.popularParts,
  'inStock': instance.inStock,
  'freeShipping': instance.freeShipping,
  'maxShippingDays': instance.maxShippingDays,
  'sortBy': instance.sortBy,
  'limit': instance.limit,
  'offset': instance.offset,
};
