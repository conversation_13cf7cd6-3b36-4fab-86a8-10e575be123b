// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AdminUserModel _$AdminUserModelFromJson(Map<String, dynamic> json) =>
    _AdminUserModel(
      id: json['id'] as String,
      email: json['email'] as String?,
      fullName: json['fullName'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      status: $enumDecode(_$UserStatusEnumMap, json['status']),
      bannedReason: json['bannedReason'] as String?,
      bannedAt: json['bannedAt'] == null
          ? null
          : DateTime.parse(json['bannedAt'] as String),
      bannedBy: json['bannedBy'] as String?,
      frozenReason: json['frozenReason'] as String?,
      frozenAt: json['frozenAt'] == null
          ? null
          : DateTime.parse(json['frozenAt'] as String),
      frozenBy: json['frozenBy'] as String?,
      frozenUntil: json['frozenUntil'] == null
          ? null
          : DateTime.parse(json['frozenUntil'] as String),
      isEmailVerified: json['isEmailVerified'] as bool,
      isPhoneVerified: json['isPhoneVerified'] as bool,
      isSeller: json['isSeller'] as bool? ?? false,
      isAdmin: json['isAdmin'] as bool? ?? false,
      lastLoginAt: json['lastLoginAt'] == null
          ? null
          : DateTime.parse(json['lastLoginAt'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      totalOrders: (json['totalOrders'] as num?)?.toInt() ?? 0,
      totalSpent: (json['totalSpent'] as num?)?.toDouble() ?? 0.0,
      violationsCount: (json['violationsCount'] as num?)?.toInt() ?? 0,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$AdminUserModelToJson(_AdminUserModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'fullName': instance.fullName,
      'phoneNumber': instance.phoneNumber,
      'status': _$UserStatusEnumMap[instance.status]!,
      'bannedReason': instance.bannedReason,
      'bannedAt': instance.bannedAt?.toIso8601String(),
      'bannedBy': instance.bannedBy,
      'frozenReason': instance.frozenReason,
      'frozenAt': instance.frozenAt?.toIso8601String(),
      'frozenBy': instance.frozenBy,
      'frozenUntil': instance.frozenUntil?.toIso8601String(),
      'isEmailVerified': instance.isEmailVerified,
      'isPhoneVerified': instance.isPhoneVerified,
      'isSeller': instance.isSeller,
      'isAdmin': instance.isAdmin,
      'lastLoginAt': instance.lastLoginAt?.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'totalOrders': instance.totalOrders,
      'totalSpent': instance.totalSpent,
      'violationsCount': instance.violationsCount,
      'notes': instance.notes,
    };

const _$UserStatusEnumMap = {
  UserStatus.active: 'active',
  UserStatus.banned: 'banned',
  UserStatus.frozen: 'frozen',
};

_UserManagementAction _$UserManagementActionFromJson(
  Map<String, dynamic> json,
) => _UserManagementAction(
  id: json['id'] as String,
  userId: json['userId'] as String,
  adminId: json['adminId'] as String,
  actionType: json['actionType'] as String,
  actionDate: DateTime.parse(json['actionDate'] as String),
  reason: json['reason'] as String?,
  expiresAt: json['expiresAt'] == null
      ? null
      : DateTime.parse(json['expiresAt'] as String),
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$UserManagementActionToJson(
  _UserManagementAction instance,
) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'adminId': instance.adminId,
  'actionType': instance.actionType,
  'actionDate': instance.actionDate.toIso8601String(),
  'reason': instance.reason,
  'expiresAt': instance.expiresAt?.toIso8601String(),
  'metadata': instance.metadata,
};
