// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatModel _$ChatModelFromJson(Map<String, dynamic> json) => ChatModel(
  id: json['id'] as String,
  buyerId: json['buyer_id'] as String,
  sellerId: json['seller_id'] as String,
  createdAt: DateTime.parse(json['created_at'] as String),
  updatedAt: DateTime.parse(json['updated_at'] as String),
  productId: json['product_id'] as String?,
  partId: json['part_id'] as String?,
  productName: json['product_name'] as String?,
  productImageUrl: json['product_image_url'] as String?,
  status:
      $enumDecodeNullable(_$ChatStatusEnumMap, json['status']) ??
      ChatStatus.active,
  lastMessage: json['last_message'] as Map<String, dynamic>?,
  unreadCount: (json['unread_count'] as num?)?.toInt() ?? 0,
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
  isDeleted: json['is_deleted'] as bool? ?? false,
);

Map<String, dynamic> _$ChatModelToJson(ChatModel instance) => <String, dynamic>{
  'id': instance.id,
  'buyer_id': instance.buyerId,
  'seller_id': instance.sellerId,
  'created_at': instance.createdAt.toIso8601String(),
  'updated_at': instance.updatedAt.toIso8601String(),
  'product_id': instance.productId,
  'part_id': instance.partId,
  'product_name': instance.productName,
  'product_image_url': instance.productImageUrl,
  'status': _$ChatStatusEnumMap[instance.status]!,
  'last_message': instance.lastMessage,
  'unread_count': instance.unreadCount,
  'metadata': instance.metadata,
  'is_deleted': instance.isDeleted,
};

const _$ChatStatusEnumMap = {
  ChatStatus.active: 'active',
  ChatStatus.archived: 'archived',
  ChatStatus.blocked: 'blocked',
  ChatStatus.deleted: 'deleted',
};

MessageModel _$MessageModelFromJson(Map<String, dynamic> json) => MessageModel(
  id: json['id'] as String,
  chatId: json['conversation_id'] as String,
  senderId: json['sender_id'] as String,
  content: json['message_text'] as String,
  timestamp: DateTime.parse(json['created_at'] as String),
  type:
      $enumDecodeNullable(_$MessageTypeEnumMap, json['type']) ??
      MessageType.text,
  status:
      $enumDecodeNullable(_$MessageStatusEnumMap, json['status']) ??
      MessageStatus.sent,
  replyToId: json['reply_to_id'] as String?,
  replyToMessage: json['reply_to_message'] as Map<String, dynamic>?,
  attachments: (json['attachments'] as List<dynamic>?)
      ?.map((e) => MessageAttachment.fromJson(e as Map<String, dynamic>))
      .toList(),
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
  isDeleted: json['is_deleted'] as bool? ?? false,
);

Map<String, dynamic> _$MessageModelToJson(MessageModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'conversation_id': instance.chatId,
      'sender_id': instance.senderId,
      'message_text': instance.content,
      'created_at': instance.timestamp.toIso8601String(),
      'type': _$MessageTypeEnumMap[instance.type]!,
      'status': _$MessageStatusEnumMap[instance.status]!,
      'reply_to_id': instance.replyToId,
      'reply_to_message': instance.replyToMessage,
      'attachments': instance.attachments,
      'metadata': instance.metadata,
      'is_deleted': instance.isDeleted,
    };

const _$MessageTypeEnumMap = {
  MessageType.text: 'text',
  MessageType.image: 'image',
  MessageType.file: 'file',
  MessageType.audio: 'audio',
  MessageType.video: 'video',
  MessageType.product: 'product',
  MessageType.system: 'system',
};

const _$MessageStatusEnumMap = {
  MessageStatus.sending: 'sending',
  MessageStatus.sent: 'sent',
  MessageStatus.delivered: 'delivered',
  MessageStatus.read: 'read',
  MessageStatus.failed: 'failed',
};

MessageAttachment _$MessageAttachmentFromJson(Map<String, dynamic> json) =>
    MessageAttachment(
      id: json['id'] as String,
      messageId: json['message_id'] as String,
      type: $enumDecode(_$AttachmentTypeEnumMap, json['type']),
      url: json['url'] as String,
      fileName: json['file_name'] as String,
      fileSize: (json['file_size'] as num?)?.toInt(),
      mimeType: json['mime_type'] as String?,
      thumbnailUrl: json['thumbnail_url'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      isDeleted: json['is_deleted'] as bool? ?? false,
    );

Map<String, dynamic> _$MessageAttachmentToJson(MessageAttachment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'message_id': instance.messageId,
      'type': _$AttachmentTypeEnumMap[instance.type]!,
      'url': instance.url,
      'file_name': instance.fileName,
      'file_size': instance.fileSize,
      'mime_type': instance.mimeType,
      'thumbnail_url': instance.thumbnailUrl,
      'metadata': instance.metadata,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'is_deleted': instance.isDeleted,
    };

const _$AttachmentTypeEnumMap = {
  AttachmentType.image: 'image',
  AttachmentType.file: 'file',
  AttachmentType.audio: 'audio',
  AttachmentType.video: 'video',
};
