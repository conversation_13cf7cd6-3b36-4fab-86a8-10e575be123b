// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SellerProfile _$SellerProfileFromJson(Map<String, dynamic> json) =>
    _SellerProfile(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      fullName: json['full_name'] as String,
      storeName: json['store_name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      whatsappNumber: json['whatsapp_number'] as String?,
      city: json['city'] as String,
      address: json['address'] as String,
      businessDescription: json['business_description'] as String?,
      businessType: json['business_type'] as String?,
      businessLicense: json['business_license'] as String?,
      taxNumber: json['tax_number'] as String?,
      businessDocuments: (json['business_documents'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      profileImageUrl: json['profile_image_url'] as String?,
      storeLogoUrl: json['store_logo_url'] as String?,
      isVerified: json['is_verified'] as bool? ?? false,
      isActive: json['is_active'] as bool? ?? false,
      rating: (json['rating'] as num?)?.toDouble(),
      totalSales: (json['total_sales'] as num?)?.toInt(),
      totalProducts: (json['total_products'] as num?)?.toInt(),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$SellerProfileToJson(_SellerProfile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'full_name': instance.fullName,
      'store_name': instance.storeName,
      'email': instance.email,
      'phone': instance.phone,
      'whatsapp_number': instance.whatsappNumber,
      'city': instance.city,
      'address': instance.address,
      'business_description': instance.businessDescription,
      'business_type': instance.businessType,
      'business_license': instance.businessLicense,
      'tax_number': instance.taxNumber,
      'business_documents': instance.businessDocuments,
      'profile_image_url': instance.profileImageUrl,
      'store_logo_url': instance.storeLogoUrl,
      'is_verified': instance.isVerified,
      'is_active': instance.isActive,
      'rating': instance.rating,
      'total_sales': instance.totalSales,
      'total_products': instance.totalProducts,
    };

_EnhancedSubscriptionRequest _$EnhancedSubscriptionRequestFromJson(
  Map<String, dynamic> json,
) => _EnhancedSubscriptionRequest(
  id: json['id'] as String,
  sellerId: json['seller_id'] as String,
  planId: json['plan_id'] as String,
  requestedTierName: json['requested_tier_name'] as String,
  billingCycleName: json['billing_cycle_name'] as String,
  status: $enumDecode(_$SubscriptionRequestStatusEnumMap, json['status']),
  statusName: json['status_name'] as String,
  requestDate: DateTime.parse(json['request_date'] as String),
  approvedDate: json['approved_date'] == null
      ? null
      : DateTime.parse(json['approved_date'] as String),
  rejectedDate: json['rejected_date'] == null
      ? null
      : DateTime.parse(json['rejected_date'] as String),
  adminId: json['admin_id'] as String?,
  adminNotes: json['admin_notes'] as String?,
  rejectionReason: json['rejection_reason'] as String?,
  requestedPriceLD: (json['requested_price_l_d'] as num).toDouble(),
  paymentMethodId: json['payment_method_id'] as String?,
  priority: (json['priority'] as num?)?.toInt(),
  sellerFullName: json['seller_full_name'] as String,
  sellerStoreName: json['seller_store_name'] as String,
  sellerEmail: json['seller_email'] as String,
  sellerPhone: json['seller_phone'] as String,
  sellerWhatsapp: json['seller_whatsapp'] as String?,
  sellerCity: json['seller_city'] as String,
  sellerAddress: json['seller_address'] as String,
  sellerBusinessType: json['seller_business_type'] as String?,
  sellerBusinessDescription: json['seller_business_description'] as String?,
  sellerProfileImageUrl: json['seller_profile_image_url'] as String?,
  sellerStoreLogoUrl: json['seller_store_logo_url'] as String?,
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
);

Map<String, dynamic> _$EnhancedSubscriptionRequestToJson(
  _EnhancedSubscriptionRequest instance,
) => <String, dynamic>{
  'id': instance.id,
  'seller_id': instance.sellerId,
  'plan_id': instance.planId,
  'requested_tier_name': instance.requestedTierName,
  'billing_cycle_name': instance.billingCycleName,
  'status': _$SubscriptionRequestStatusEnumMap[instance.status]!,
  'status_name': instance.statusName,
  'request_date': instance.requestDate.toIso8601String(),
  'approved_date': instance.approvedDate?.toIso8601String(),
  'rejected_date': instance.rejectedDate?.toIso8601String(),
  'admin_id': instance.adminId,
  'admin_notes': instance.adminNotes,
  'rejection_reason': instance.rejectionReason,
  'requested_price_l_d': instance.requestedPriceLD,
  'payment_method_id': instance.paymentMethodId,
  'priority': instance.priority,
  'seller_full_name': instance.sellerFullName,
  'seller_store_name': instance.sellerStoreName,
  'seller_email': instance.sellerEmail,
  'seller_phone': instance.sellerPhone,
  'seller_whatsapp': instance.sellerWhatsapp,
  'seller_city': instance.sellerCity,
  'seller_address': instance.sellerAddress,
  'seller_business_type': instance.sellerBusinessType,
  'seller_business_description': instance.sellerBusinessDescription,
  'seller_profile_image_url': instance.sellerProfileImageUrl,
  'seller_store_logo_url': instance.sellerStoreLogoUrl,
};

const _$SubscriptionRequestStatusEnumMap = {
  SubscriptionRequestStatus.pending: 'pending',
  SubscriptionRequestStatus.underReview: 'under_review',
  SubscriptionRequestStatus.approved: 'approved',
  SubscriptionRequestStatus.rejected: 'rejected',
  SubscriptionRequestStatus.cancelled: 'cancelled',
  SubscriptionRequestStatus.expired: 'expired',
};
