// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_RefundStatistics _$RefundStatisticsFromJson(Map<String, dynamic> json) =>
    _RefundStatistics(
      totalRefunds: (json['total_refunds'] as num).toInt(),
      pendingRefunds: (json['pending_refunds'] as num).toInt(),
      approvedRefunds: (json['approved_refunds'] as num).toInt(),
      rejectedRefunds: (json['rejected_refunds'] as num).toInt(),
      processingRefunds: (json['processing_refunds'] as num).toInt(),
      completedRefunds: (json['completed_refunds'] as num).toInt(),
      failedRefunds: (json['failed_refunds'] as num).toInt(),
      cancelledRefunds: (json['cancelled_refunds'] as num).toInt(),
      totalRefundAmount: (json['total_refund_amount'] as num).toDouble(),
      averageRefundAmount: (json['average_refund_amount'] as num).toDouble(),
    );

Map<String, dynamic> _$RefundStatisticsToJson(_RefundStatistics instance) =>
    <String, dynamic>{
      'total_refunds': instance.totalRefunds,
      'pending_refunds': instance.pendingRefunds,
      'approved_refunds': instance.approvedRefunds,
      'rejected_refunds': instance.rejectedRefunds,
      'processing_refunds': instance.processingRefunds,
      'completed_refunds': instance.completedRefunds,
      'failed_refunds': instance.failedRefunds,
      'cancelled_refunds': instance.cancelledRefunds,
      'total_refund_amount': instance.totalRefundAmount,
      'average_refund_amount': instance.averageRefundAmount,
    };
