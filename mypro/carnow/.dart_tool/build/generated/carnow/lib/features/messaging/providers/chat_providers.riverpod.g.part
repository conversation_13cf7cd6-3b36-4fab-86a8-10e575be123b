// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userChatsHash() => r'07614c354b71727ea5fa15bb889de9bb9a5a3faa';

/// مزود قائمة المحادثات - FIXED: Converted to @riverpod pattern
///
/// Copied from [userChats].
@ProviderFor(userChats)
final userChatsProvider = AutoDisposeFutureProvider<List<ChatModel>>.internal(
  userChats,
  name: r'userChatsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userChatsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserChatsRef = AutoDisposeFutureProviderRef<List<ChatModel>>;
String _$chatMessagesHash() => r'798247ffa6d3aa9ae354784bcf02acc7bffc8d35';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// مزود رسائل المحادثة - FIXED: Converted to @riverpod pattern
///
/// Copied from [chatMessages].
@ProviderFor(chatMessages)
const chatMessagesProvider = ChatMessagesFamily();

/// مزود رسائل المحادثة - FIXED: Converted to @riverpod pattern
///
/// Copied from [chatMessages].
class ChatMessagesFamily extends Family<AsyncValue<List<MessageModel>>> {
  /// مزود رسائل المحادثة - FIXED: Converted to @riverpod pattern
  ///
  /// Copied from [chatMessages].
  const ChatMessagesFamily();

  /// مزود رسائل المحادثة - FIXED: Converted to @riverpod pattern
  ///
  /// Copied from [chatMessages].
  ChatMessagesProvider call(String chatId) {
    return ChatMessagesProvider(chatId);
  }

  @override
  ChatMessagesProvider getProviderOverride(
    covariant ChatMessagesProvider provider,
  ) {
    return call(provider.chatId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'chatMessagesProvider';
}

/// مزود رسائل المحادثة - FIXED: Converted to @riverpod pattern
///
/// Copied from [chatMessages].
class ChatMessagesProvider
    extends AutoDisposeFutureProvider<List<MessageModel>> {
  /// مزود رسائل المحادثة - FIXED: Converted to @riverpod pattern
  ///
  /// Copied from [chatMessages].
  ChatMessagesProvider(String chatId)
    : this._internal(
        (ref) => chatMessages(ref as ChatMessagesRef, chatId),
        from: chatMessagesProvider,
        name: r'chatMessagesProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$chatMessagesHash,
        dependencies: ChatMessagesFamily._dependencies,
        allTransitiveDependencies:
            ChatMessagesFamily._allTransitiveDependencies,
        chatId: chatId,
      );

  ChatMessagesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.chatId,
  }) : super.internal();

  final String chatId;

  @override
  Override overrideWith(
    FutureOr<List<MessageModel>> Function(ChatMessagesRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ChatMessagesProvider._internal(
        (ref) => create(ref as ChatMessagesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        chatId: chatId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<MessageModel>> createElement() {
    return _ChatMessagesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ChatMessagesProvider && other.chatId == chatId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, chatId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ChatMessagesRef on AutoDisposeFutureProviderRef<List<MessageModel>> {
  /// The parameter `chatId` of this provider.
  String get chatId;
}

class _ChatMessagesProviderElement
    extends AutoDisposeFutureProviderElement<List<MessageModel>>
    with ChatMessagesRef {
  _ChatMessagesProviderElement(super.provider);

  @override
  String get chatId => (origin as ChatMessagesProvider).chatId;
}

String _$chatSearchHash() => r'd0ae9d767da2c75243de0117d8ff5f5d4b928b19';

/// مزود البحث في المحادثات - FIXED: Converted to @riverpod pattern
///
/// Copied from [chatSearch].
@ProviderFor(chatSearch)
const chatSearchProvider = ChatSearchFamily();

/// مزود البحث في المحادثات - FIXED: Converted to @riverpod pattern
///
/// Copied from [chatSearch].
class ChatSearchFamily extends Family<AsyncValue<List<ChatModel>>> {
  /// مزود البحث في المحادثات - FIXED: Converted to @riverpod pattern
  ///
  /// Copied from [chatSearch].
  const ChatSearchFamily();

  /// مزود البحث في المحادثات - FIXED: Converted to @riverpod pattern
  ///
  /// Copied from [chatSearch].
  ChatSearchProvider call(String query) {
    return ChatSearchProvider(query);
  }

  @override
  ChatSearchProvider getProviderOverride(
    covariant ChatSearchProvider provider,
  ) {
    return call(provider.query);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'chatSearchProvider';
}

/// مزود البحث في المحادثات - FIXED: Converted to @riverpod pattern
///
/// Copied from [chatSearch].
class ChatSearchProvider extends AutoDisposeFutureProvider<List<ChatModel>> {
  /// مزود البحث في المحادثات - FIXED: Converted to @riverpod pattern
  ///
  /// Copied from [chatSearch].
  ChatSearchProvider(String query)
    : this._internal(
        (ref) => chatSearch(ref as ChatSearchRef, query),
        from: chatSearchProvider,
        name: r'chatSearchProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$chatSearchHash,
        dependencies: ChatSearchFamily._dependencies,
        allTransitiveDependencies: ChatSearchFamily._allTransitiveDependencies,
        query: query,
      );

  ChatSearchProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final String query;

  @override
  Override overrideWith(
    FutureOr<List<ChatModel>> Function(ChatSearchRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ChatSearchProvider._internal(
        (ref) => create(ref as ChatSearchRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ChatModel>> createElement() {
    return _ChatSearchProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ChatSearchProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ChatSearchRef on AutoDisposeFutureProviderRef<List<ChatModel>> {
  /// The parameter `query` of this provider.
  String get query;
}

class _ChatSearchProviderElement
    extends AutoDisposeFutureProviderElement<List<ChatModel>>
    with ChatSearchRef {
  _ChatSearchProviderElement(super.provider);

  @override
  String get query => (origin as ChatSearchProvider).query;
}

String _$messageSenderHash() => r'24234e5c0ec7990cb849663af4a37de50b3b5a21';

/// مزود إرسال الرسائل
///
/// Copied from [MessageSender].
@ProviderFor(MessageSender)
final messageSenderProvider =
    AutoDisposeNotifierProvider<MessageSender, AsyncValue<void>>.internal(
      MessageSender.new,
      name: r'messageSenderProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$messageSenderHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$MessageSender = AutoDisposeNotifier<AsyncValue<void>>;
String _$chatCreatorHash() => r'876d72d7b23ed85ef192a2d20b1682ef1bbb48e3';

/// مزود إنشاء محادثة جديدة
///
/// Copied from [ChatCreator].
@ProviderFor(ChatCreator)
final chatCreatorProvider =
    AutoDisposeNotifierProvider<ChatCreator, AsyncValue<ChatModel?>>.internal(
      ChatCreator.new,
      name: r'chatCreatorProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$chatCreatorHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$ChatCreator = AutoDisposeNotifier<AsyncValue<ChatModel?>>;
String _$chatArchiverHash() => r'785b06a414feced2d0c2cb7f442ff469e30f0e6a';

/// مزود أرشفة المحادثة
///
/// Copied from [ChatArchiver].
@ProviderFor(ChatArchiver)
final chatArchiverProvider =
    AutoDisposeNotifierProvider<ChatArchiver, AsyncValue<void>>.internal(
      ChatArchiver.new,
      name: r'chatArchiverProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$chatArchiverHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$ChatArchiver = AutoDisposeNotifier<AsyncValue<void>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
