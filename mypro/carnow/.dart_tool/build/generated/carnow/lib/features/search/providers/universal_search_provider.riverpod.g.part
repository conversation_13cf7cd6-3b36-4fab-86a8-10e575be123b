// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$universalCategoriesHash() =>
    r'84a2100d0adf2bda2abc50a768e89e4880397d77';

/// Categories Provider
///
/// Copied from [universalCategories].
@ProviderFor(universalCategories)
final universalCategoriesProvider =
    AutoDisposeFutureProvider<List<dynamic>>.internal(
      universalCategories,
      name: r'universalCategoriesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$universalCategoriesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UniversalCategoriesRef = AutoDisposeFutureProviderRef<List<dynamic>>;
String _$searchResultsHash() => r'6bbab51abf9dbcbc1d8b5ebbae9598fca83707d3';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Simple search provider following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Copied from [searchResults].
@ProviderFor(searchResults)
const searchResultsProvider = SearchResultsFamily();

/// Simple search provider following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Copied from [searchResults].
class SearchResultsFamily extends Family<AsyncValue<List<dynamic>>> {
  /// Simple search provider following Forever Plan Architecture
  /// Flutter (UI Only) → Go API → Supabase (Data Only)
  ///
  /// Copied from [searchResults].
  const SearchResultsFamily();

  /// Simple search provider following Forever Plan Architecture
  /// Flutter (UI Only) → Go API → Supabase (Data Only)
  ///
  /// Copied from [searchResults].
  SearchResultsProvider call(String query) {
    return SearchResultsProvider(query);
  }

  @override
  SearchResultsProvider getProviderOverride(
    covariant SearchResultsProvider provider,
  ) {
    return call(provider.query);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'searchResultsProvider';
}

/// Simple search provider following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Copied from [searchResults].
class SearchResultsProvider extends AutoDisposeFutureProvider<List<dynamic>> {
  /// Simple search provider following Forever Plan Architecture
  /// Flutter (UI Only) → Go API → Supabase (Data Only)
  ///
  /// Copied from [searchResults].
  SearchResultsProvider(String query)
    : this._internal(
        (ref) => searchResults(ref as SearchResultsRef, query),
        from: searchResultsProvider,
        name: r'searchResultsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$searchResultsHash,
        dependencies: SearchResultsFamily._dependencies,
        allTransitiveDependencies:
            SearchResultsFamily._allTransitiveDependencies,
        query: query,
      );

  SearchResultsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final String query;

  @override
  Override overrideWith(
    FutureOr<List<dynamic>> Function(SearchResultsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SearchResultsProvider._internal(
        (ref) => create(ref as SearchResultsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<dynamic>> createElement() {
    return _SearchResultsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SearchResultsProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SearchResultsRef on AutoDisposeFutureProviderRef<List<dynamic>> {
  /// The parameter `query` of this provider.
  String get query;
}

class _SearchResultsProviderElement
    extends AutoDisposeFutureProviderElement<List<dynamic>>
    with SearchResultsRef {
  _SearchResultsProviderElement(super.provider);

  @override
  String get query => (origin as SearchResultsProvider).query;
}

String _$searchSuggestionsHash() => r'cce4335f830b26d30f418362a83bb613dd5493b6';

/// Search suggestions provider
///
/// Copied from [searchSuggestions].
@ProviderFor(searchSuggestions)
const searchSuggestionsProvider = SearchSuggestionsFamily();

/// Search suggestions provider
///
/// Copied from [searchSuggestions].
class SearchSuggestionsFamily extends Family<AsyncValue<List<String>>> {
  /// Search suggestions provider
  ///
  /// Copied from [searchSuggestions].
  const SearchSuggestionsFamily();

  /// Search suggestions provider
  ///
  /// Copied from [searchSuggestions].
  SearchSuggestionsProvider call(String query) {
    return SearchSuggestionsProvider(query);
  }

  @override
  SearchSuggestionsProvider getProviderOverride(
    covariant SearchSuggestionsProvider provider,
  ) {
    return call(provider.query);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'searchSuggestionsProvider';
}

/// Search suggestions provider
///
/// Copied from [searchSuggestions].
class SearchSuggestionsProvider
    extends AutoDisposeFutureProvider<List<String>> {
  /// Search suggestions provider
  ///
  /// Copied from [searchSuggestions].
  SearchSuggestionsProvider(String query)
    : this._internal(
        (ref) => searchSuggestions(ref as SearchSuggestionsRef, query),
        from: searchSuggestionsProvider,
        name: r'searchSuggestionsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$searchSuggestionsHash,
        dependencies: SearchSuggestionsFamily._dependencies,
        allTransitiveDependencies:
            SearchSuggestionsFamily._allTransitiveDependencies,
        query: query,
      );

  SearchSuggestionsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final String query;

  @override
  Override overrideWith(
    FutureOr<List<String>> Function(SearchSuggestionsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SearchSuggestionsProvider._internal(
        (ref) => create(ref as SearchSuggestionsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<String>> createElement() {
    return _SearchSuggestionsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SearchSuggestionsProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SearchSuggestionsRef on AutoDisposeFutureProviderRef<List<String>> {
  /// The parameter `query` of this provider.
  String get query;
}

class _SearchSuggestionsProviderElement
    extends AutoDisposeFutureProviderElement<List<String>>
    with SearchSuggestionsRef {
  _SearchSuggestionsProviderElement(super.provider);

  @override
  String get query => (origin as SearchSuggestionsProvider).query;
}

String _$recentSearchesHash() => r'93e2cc273185328eb05cfc9ca2f727f178098106';

/// Recent searches provider
///
/// Copied from [recentSearches].
@ProviderFor(recentSearches)
final recentSearchesProvider = AutoDisposeFutureProvider<List<String>>.internal(
  recentSearches,
  name: r'recentSearchesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$recentSearchesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef RecentSearchesRef = AutoDisposeFutureProviderRef<List<String>>;
String _$universalSearchHash() => r'd83e2063e67a18d68e6bbadf460ad50e61b5c3e9';

/// Universal Search Provider
///
/// Copied from [UniversalSearch].
@ProviderFor(UniversalSearch)
final universalSearchProvider =
    AutoDisposeNotifierProvider<UniversalSearch, UniversalSearchState>.internal(
      UniversalSearch.new,
      name: r'universalSearchProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$universalSearchHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$UniversalSearch = AutoDisposeNotifier<UniversalSearchState>;
String _$quickSearchHash() => r'6c671420add6ff8d6ddc53b5210fc0517128ce30';

/// Quick Search Provider
///
/// Copied from [QuickSearch].
@ProviderFor(QuickSearch)
final quickSearchProvider =
    AutoDisposeNotifierProvider<QuickSearch, List<String>>.internal(
      QuickSearch.new,
      name: r'quickSearchProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$quickSearchHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$QuickSearch = AutoDisposeNotifier<List<String>>;
String _$searchHistoryHash() => r'827d8c122d00d403b4a2e3e54d59e16a663cd6f0';

/// Search History Provider
///
/// Copied from [SearchHistory].
@ProviderFor(SearchHistory)
final searchHistoryProvider =
    AutoDisposeNotifierProvider<SearchHistory, List<String>>.internal(
      SearchHistory.new,
      name: r'searchHistoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$searchHistoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SearchHistory = AutoDisposeNotifier<List<String>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
