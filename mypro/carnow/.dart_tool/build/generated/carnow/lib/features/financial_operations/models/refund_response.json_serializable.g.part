// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_RefundResponse _$RefundResponseFromJson(Map<String, dynamic> json) =>
    _RefundResponse(
      success: json['success'] as bool,
      refundId: json['refund_id'] as String?,
      message: json['message'] as String?,
      error: json['error'] as String?,
    );

Map<String, dynamic> _$RefundResponseToJson(_RefundResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'refund_id': instance.refundId,
      'message': instance.message,
      'error': instance.error,
    };
