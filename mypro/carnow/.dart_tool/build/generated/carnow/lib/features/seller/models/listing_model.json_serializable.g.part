// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ProductListing _$ProductListingFromJson(Map<String, dynamic> json) =>
    _ProductListing(
      id: json['id'] as String,
      sellerId: json['seller_id'] as String,
      productId: json['product_id'] as String,
      status: $enumDecode(_$ListingStatusEnumMap, json['status']),
      listedAt: DateTime.parse(json['listed_at'] as String),
      expiresAt: DateTime.parse(json['expires_at'] as String),
      renewedAt: json['renewed_at'] == null
          ? null
          : DateTime.parse(json['renewed_at'] as String),
      pausedAt: json['paused_at'] == null
          ? null
          : DateTime.parse(json['paused_at'] as String),
      suspendedAt: json['suspended_at'] == null
          ? null
          : DateTime.parse(json['suspended_at'] as String),
      validityDays: (json['validity_days'] as num?)?.toInt() ?? 30,
      isFromFreeQuota: json['is_from_free_quota'] as bool? ?? false,
      isPaid: json['is_paid'] as bool? ?? false,
      hasPriority: json['has_priority'] as bool? ?? false,
      listingFee: (json['listing_fee'] as num?)?.toDouble(),
      paymentTransactionId: json['payment_transaction_id'] as String?,
      paymentStatus: $enumDecodeNullable(
        _$ListingPaymentStatusEnumMap,
        json['payment_status'],
      ),
      subscriptionTierAtListing:
          json['subscription_tier_at_listing'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$ProductListingToJson(_ProductListing instance) =>
    <String, dynamic>{
      'id': instance.id,
      'seller_id': instance.sellerId,
      'product_id': instance.productId,
      'status': _$ListingStatusEnumMap[instance.status]!,
      'listed_at': instance.listedAt.toIso8601String(),
      'expires_at': instance.expiresAt.toIso8601String(),
      'renewed_at': instance.renewedAt?.toIso8601String(),
      'paused_at': instance.pausedAt?.toIso8601String(),
      'suspended_at': instance.suspendedAt?.toIso8601String(),
      'validity_days': instance.validityDays,
      'is_from_free_quota': instance.isFromFreeQuota,
      'is_paid': instance.isPaid,
      'has_priority': instance.hasPriority,
      'listing_fee': instance.listingFee,
      'payment_transaction_id': instance.paymentTransactionId,
      'payment_status': _$ListingPaymentStatusEnumMap[instance.paymentStatus],
      'subscription_tier_at_listing': instance.subscriptionTierAtListing,
      'metadata': instance.metadata,
    };

const _$ListingStatusEnumMap = {
  ListingStatus.draft: 'draft',
  ListingStatus.active: 'active',
  ListingStatus.expired: 'expired',
  ListingStatus.paused: 'paused',
  ListingStatus.suspended: 'suspended',
  ListingStatus.sold: 'sold',
  ListingStatus.deleted: 'deleted',
};

const _$ListingPaymentStatusEnumMap = {
  ListingPaymentStatus.pending: 'pending',
  ListingPaymentStatus.paid: 'paid',
  ListingPaymentStatus.failed: 'failed',
  ListingPaymentStatus.refunded: 'refunded',
  ListingPaymentStatus.waived: 'waived',
  ListingPaymentStatus.overdue: 'overdue',
};

_ListingFeeTransaction _$ListingFeeTransactionFromJson(
  Map<String, dynamic> json,
) => _ListingFeeTransaction(
  id: json['id'] as String,
  sellerId: json['seller_id'] as String,
  listingId: json['listing_id'] as String,
  amount: (json['amount'] as num).toDouble(),
  currency: json['currency'] as String,
  status: $enumDecode(_$ListingPaymentStatusEnumMap, json['status']),
  createdAt: DateTime.parse(json['created_at'] as String),
  paidAt: json['paid_at'] == null
      ? null
      : DateTime.parse(json['paid_at'] as String),
  failedAt: json['failed_at'] == null
      ? null
      : DateTime.parse(json['failed_at'] as String),
  refundedAt: json['refunded_at'] == null
      ? null
      : DateTime.parse(json['refunded_at'] as String),
  paymentMethodId: json['payment_method_id'] as String?,
  transactionReference: json['transaction_reference'] as String?,
  failureReason: json['failure_reason'] as String?,
  paymentMetadata: json['payment_metadata'] as Map<String, dynamic>?,
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
);

Map<String, dynamic> _$ListingFeeTransactionToJson(
  _ListingFeeTransaction instance,
) => <String, dynamic>{
  'id': instance.id,
  'seller_id': instance.sellerId,
  'listing_id': instance.listingId,
  'amount': instance.amount,
  'currency': instance.currency,
  'status': _$ListingPaymentStatusEnumMap[instance.status]!,
  'created_at': instance.createdAt.toIso8601String(),
  'paid_at': instance.paidAt?.toIso8601String(),
  'failed_at': instance.failedAt?.toIso8601String(),
  'refunded_at': instance.refundedAt?.toIso8601String(),
  'payment_method_id': instance.paymentMethodId,
  'transaction_reference': instance.transactionReference,
  'failure_reason': instance.failureReason,
  'payment_metadata': instance.paymentMetadata,
};

_MonthlyListingQuota _$MonthlyListingQuotaFromJson(Map<String, dynamic> json) =>
    _MonthlyListingQuota(
      id: json['id'] as String,
      sellerId: json['seller_id'] as String,
      subscriptionTier: $enumDecode(
        _$SubscriptionTierEnumMap,
        json['subscription_tier'],
      ),
      year: (json['year'] as num).toInt(),
      month: (json['month'] as num).toInt(),
      totalFreeListings: (json['total_free_listings'] as num).toInt(),
      usedFreeListings: (json['used_free_listings'] as num?)?.toInt() ?? 0,
      paidListings: (json['paid_listings'] as num?)?.toInt() ?? 0,
      totalFeesPaid: (json['total_fees_paid'] as num?)?.toDouble() ?? 0.0,
      periodStart: DateTime.parse(json['period_start'] as String),
      periodEnd: DateTime.parse(json['period_end'] as String),
      freeListingIds: (json['free_listing_ids'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      paidListingIds: (json['paid_listing_ids'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$MonthlyListingQuotaToJson(
  _MonthlyListingQuota instance,
) => <String, dynamic>{
  'id': instance.id,
  'seller_id': instance.sellerId,
  'subscription_tier': _$SubscriptionTierEnumMap[instance.subscriptionTier]!,
  'year': instance.year,
  'month': instance.month,
  'total_free_listings': instance.totalFreeListings,
  'used_free_listings': instance.usedFreeListings,
  'paid_listings': instance.paidListings,
  'total_fees_paid': instance.totalFeesPaid,
  'period_start': instance.periodStart.toIso8601String(),
  'period_end': instance.periodEnd.toIso8601String(),
  'free_listing_ids': instance.freeListingIds,
  'paid_listing_ids': instance.paidListingIds,
};

const _$SubscriptionTierEnumMap = {
  SubscriptionTier.starter: 'starter',
  SubscriptionTier.basic: 'basic',
  SubscriptionTier.premium: 'premium',
  SubscriptionTier.anchor: 'anchor',
  SubscriptionTier.enterprise: 'enterprise',
};

_ListingRenewalRequest _$ListingRenewalRequestFromJson(
  Map<String, dynamic> json,
) => _ListingRenewalRequest(
  id: json['id'] as String,
  sellerId: json['seller_id'] as String,
  listingId: json['listing_id'] as String,
  requestedAt: DateTime.parse(json['requested_at'] as String),
  renewalDays: (json['renewal_days'] as num).toInt(),
  useFreeQuota: json['use_free_quota'] as bool,
  renewalFee: (json['renewal_fee'] as num?)?.toDouble(),
  paymentStatus: $enumDecodeNullable(
    _$ListingPaymentStatusEnumMap,
    json['payment_status'],
  ),
  paymentTransactionId: json['payment_transaction_id'] as String?,
  processedAt: json['processed_at'] == null
      ? null
      : DateTime.parse(json['processed_at'] as String),
  rejectionReason: json['rejection_reason'] as String?,
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
);

Map<String, dynamic> _$ListingRenewalRequestToJson(
  _ListingRenewalRequest instance,
) => <String, dynamic>{
  'id': instance.id,
  'seller_id': instance.sellerId,
  'listing_id': instance.listingId,
  'requested_at': instance.requestedAt.toIso8601String(),
  'renewal_days': instance.renewalDays,
  'use_free_quota': instance.useFreeQuota,
  'renewal_fee': instance.renewalFee,
  'payment_status': _$ListingPaymentStatusEnumMap[instance.paymentStatus],
  'payment_transaction_id': instance.paymentTransactionId,
  'processed_at': instance.processedAt?.toIso8601String(),
  'rejection_reason': instance.rejectionReason,
};

_BulkListingOperation _$BulkListingOperationFromJson(
  Map<String, dynamic> json,
) => _BulkListingOperation(
  id: json['id'] as String,
  sellerId: json['seller_id'] as String,
  productIds: (json['product_ids'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  initiatedAt: DateTime.parse(json['initiated_at'] as String),
  subscriptionTier: $enumDecode(
    _$SubscriptionTierEnumMap,
    json['subscription_tier'],
  ),
  successfulListings: (json['successful_listings'] as num?)?.toInt() ?? 0,
  failedListings: (json['failed_listings'] as num?)?.toInt() ?? 0,
  usedFreeQuota: (json['used_free_quota'] as num?)?.toInt() ?? 0,
  paidListings: (json['paid_listings'] as num?)?.toInt() ?? 0,
  totalFees: (json['total_fees'] as num?)?.toDouble() ?? 0.0,
  failures: (json['failures'] as Map<String, dynamic>?)?.map(
    (k, e) => MapEntry(k, e as String),
  ),
  successfulListingIds: (json['successful_listing_ids'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  completedAt: json['completed_at'] == null
      ? null
      : DateTime.parse(json['completed_at'] as String),
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
);

Map<String, dynamic> _$BulkListingOperationToJson(
  _BulkListingOperation instance,
) => <String, dynamic>{
  'id': instance.id,
  'seller_id': instance.sellerId,
  'product_ids': instance.productIds,
  'initiated_at': instance.initiatedAt.toIso8601String(),
  'subscription_tier': _$SubscriptionTierEnumMap[instance.subscriptionTier]!,
  'successful_listings': instance.successfulListings,
  'failed_listings': instance.failedListings,
  'used_free_quota': instance.usedFreeQuota,
  'paid_listings': instance.paidListings,
  'total_fees': instance.totalFees,
  'failures': instance.failures,
  'successful_listing_ids': instance.successfulListingIds,
  'completed_at': instance.completedAt?.toIso8601String(),
};
