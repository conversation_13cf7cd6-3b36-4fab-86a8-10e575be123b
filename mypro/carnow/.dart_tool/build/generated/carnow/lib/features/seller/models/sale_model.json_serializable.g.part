// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SaleModel _$SaleModelFromJson(Map<String, dynamic> json) => _SaleModel(
  id: (json['id'] as num?)?.toInt(),
  productId: (json['productId'] as num?)?.toInt(),
  productName: json['productName'] as String?,
  price: (json['price'] as num?)?.toDouble(),
  quantity: (json['quantity'] as num?)?.toInt(),
  totalAmount: (json['totalAmount'] as num?)?.toDouble(),
  customerId: (json['customerId'] as num?)?.toInt(),
  customerName: json['customerName'] as String?,
  saleDate: json['saleDate'] == null
      ? null
      : DateTime.parse(json['saleDate'] as String),
  status:
      $enumDecodeNullable(_$SaleStatusEnumMap, json['status']) ??
      SaleStatus.completed,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$SaleModelToJson(_SaleModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'productId': instance.productId,
      'productName': instance.productName,
      'price': instance.price,
      'quantity': instance.quantity,
      'totalAmount': instance.totalAmount,
      'customerId': instance.customerId,
      'customerName': instance.customerName,
      'saleDate': instance.saleDate?.toIso8601String(),
      'status': _$SaleStatusEnumMap[instance.status]!,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$SaleStatusEnumMap = {
  SaleStatus.pending: 0,
  SaleStatus.processing: 1,
  SaleStatus.shipped: 2,
  SaleStatus.completed: 3,
  SaleStatus.cancelled: 4,
  SaleStatus.refunded: 5,
};
