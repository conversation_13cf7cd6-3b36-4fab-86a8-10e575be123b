// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_RefundDecision _$RefundDecisionFromJson(Map<String, dynamic> json) =>
    _RefundDecision(
      refundId: json['refund_id'] as String,
      decision: $enumDecode(_$RefundDecisionActionEnumMap, json['decision']),
      adminNotes: json['admin_notes'] as String?,
    );

Map<String, dynamic> _$RefundDecisionToJson(_RefundDecision instance) =>
    <String, dynamic>{
      'refund_id': instance.refundId,
      'decision': _$RefundDecisionActionEnumMap[instance.decision]!,
      'admin_notes': instance.adminNotes,
    };

const _$RefundDecisionActionEnumMap = {
  RefundDecisionAction.approve: 'approve',
  RefundDecisionAction.reject: 'reject',
};
