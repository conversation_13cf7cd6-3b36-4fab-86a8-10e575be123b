// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$stockAlertsHash() => r'e5e05f2ce6912b64bf6b7deb432584f004a94b7a';

/// مزود تنبيهات المخزون
/// ✅ Updated to use SimpleApiClient (Forever Plan Architecture)
///
/// Copied from [stockAlerts].
@ProviderFor(stockAlerts)
final stockAlertsProvider =
    AutoDisposeFutureProvider<List<Map<String, dynamic>>>.internal(
      stockAlerts,
      name: r'stockAlertsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$stockAlertsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef StockAlertsRef =
    AutoDisposeFutureProviderRef<List<Map<String, dynamic>>>;
String _$inventoryStatsHash() => r'e5411d664aff6a886e046362e4413ac410622ae2';

/// مزود إحصائيات المخزون
/// ✅ Updated to use SimpleApiClient (Forever Plan Architecture)
///
/// Copied from [inventoryStats].
@ProviderFor(inventoryStats)
final inventoryStatsProvider =
    AutoDisposeFutureProvider<InventoryStats>.internal(
      inventoryStats,
      name: r'inventoryStatsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$inventoryStatsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef InventoryStatsRef = AutoDisposeFutureProviderRef<InventoryStats>;
String _$inventoryManagementHash() =>
    r'4cdd30291f549baef2142f391762c2063ef4ec6c';

/// مزود إدارة المخزون المتكامل
/// ✅ Updated to use SimpleApiClient (Forever Plan Architecture)
///
/// Copied from [InventoryManagement].
@ProviderFor(InventoryManagement)
final inventoryManagementProvider =
    AutoDisposeAsyncNotifierProvider<
      InventoryManagement,
      List<SellerInventoryItem>
    >.internal(
      InventoryManagement.new,
      name: r'inventoryManagementProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$inventoryManagementHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$InventoryManagement =
    AutoDisposeAsyncNotifier<List<SellerInventoryItem>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
