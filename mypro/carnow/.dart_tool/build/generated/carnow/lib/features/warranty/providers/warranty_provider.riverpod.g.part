// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userWarrantiesHash() => r'bfa6baa8ad9577625fd9de02be4228a20aa58edf';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider للحصول على ضمانات المستخدم
///
/// Copied from [userWarranties].
@ProviderFor(userWarranties)
const userWarrantiesProvider = UserWarrantiesFamily();

/// Provider للحصول على ضمانات المستخدم
///
/// Copied from [userWarranties].
class UserWarrantiesFamily extends Family<AsyncValue<List<Warranty>>> {
  /// Provider للحصول على ضمانات المستخدم
  ///
  /// Copied from [userWarranties].
  const UserWarrantiesFamily();

  /// Provider للحصول على ضمانات المستخدم
  ///
  /// Copied from [userWarranties].
  UserWarrantiesProvider call(String userId) {
    return UserWarrantiesProvider(userId);
  }

  @override
  UserWarrantiesProvider getProviderOverride(
    covariant UserWarrantiesProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'userWarrantiesProvider';
}

/// Provider للحصول على ضمانات المستخدم
///
/// Copied from [userWarranties].
class UserWarrantiesProvider extends AutoDisposeFutureProvider<List<Warranty>> {
  /// Provider للحصول على ضمانات المستخدم
  ///
  /// Copied from [userWarranties].
  UserWarrantiesProvider(String userId)
    : this._internal(
        (ref) => userWarranties(ref as UserWarrantiesRef, userId),
        from: userWarrantiesProvider,
        name: r'userWarrantiesProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$userWarrantiesHash,
        dependencies: UserWarrantiesFamily._dependencies,
        allTransitiveDependencies:
            UserWarrantiesFamily._allTransitiveDependencies,
        userId: userId,
      );

  UserWarrantiesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    FutureOr<List<Warranty>> Function(UserWarrantiesRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: UserWarrantiesProvider._internal(
        (ref) => create(ref as UserWarrantiesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Warranty>> createElement() {
    return _UserWarrantiesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UserWarrantiesProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin UserWarrantiesRef on AutoDisposeFutureProviderRef<List<Warranty>> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _UserWarrantiesProviderElement
    extends AutoDisposeFutureProviderElement<List<Warranty>>
    with UserWarrantiesRef {
  _UserWarrantiesProviderElement(super.provider);

  @override
  String get userId => (origin as UserWarrantiesProvider).userId;
}

String _$productWarrantiesHash() => r'a25b90f671f5d14a687db20695f7aecb7a2b1340';

/// Provider للحصول على ضمانات المنتج
///
/// Copied from [productWarranties].
@ProviderFor(productWarranties)
const productWarrantiesProvider = ProductWarrantiesFamily();

/// Provider للحصول على ضمانات المنتج
///
/// Copied from [productWarranties].
class ProductWarrantiesFamily extends Family<AsyncValue<List<Warranty>>> {
  /// Provider للحصول على ضمانات المنتج
  ///
  /// Copied from [productWarranties].
  const ProductWarrantiesFamily();

  /// Provider للحصول على ضمانات المنتج
  ///
  /// Copied from [productWarranties].
  ProductWarrantiesProvider call(String productId) {
    return ProductWarrantiesProvider(productId);
  }

  @override
  ProductWarrantiesProvider getProviderOverride(
    covariant ProductWarrantiesProvider provider,
  ) {
    return call(provider.productId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'productWarrantiesProvider';
}

/// Provider للحصول على ضمانات المنتج
///
/// Copied from [productWarranties].
class ProductWarrantiesProvider
    extends AutoDisposeFutureProvider<List<Warranty>> {
  /// Provider للحصول على ضمانات المنتج
  ///
  /// Copied from [productWarranties].
  ProductWarrantiesProvider(String productId)
    : this._internal(
        (ref) => productWarranties(ref as ProductWarrantiesRef, productId),
        from: productWarrantiesProvider,
        name: r'productWarrantiesProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$productWarrantiesHash,
        dependencies: ProductWarrantiesFamily._dependencies,
        allTransitiveDependencies:
            ProductWarrantiesFamily._allTransitiveDependencies,
        productId: productId,
      );

  ProductWarrantiesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.productId,
  }) : super.internal();

  final String productId;

  @override
  Override overrideWith(
    FutureOr<List<Warranty>> Function(ProductWarrantiesRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProductWarrantiesProvider._internal(
        (ref) => create(ref as ProductWarrantiesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        productId: productId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Warranty>> createElement() {
    return _ProductWarrantiesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProductWarrantiesProvider && other.productId == productId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, productId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ProductWarrantiesRef on AutoDisposeFutureProviderRef<List<Warranty>> {
  /// The parameter `productId` of this provider.
  String get productId;
}

class _ProductWarrantiesProviderElement
    extends AutoDisposeFutureProviderElement<List<Warranty>>
    with ProductWarrantiesRef {
  _ProductWarrantiesProviderElement(super.provider);

  @override
  String get productId => (origin as ProductWarrantiesProvider).productId;
}

String _$activeWarrantiesHash() => r'57996773fe32230c9a09b7fe289b72def15e57e4';

/// Provider للحصول على الضمانات النشطة
///
/// Copied from [activeWarranties].
@ProviderFor(activeWarranties)
final activeWarrantiesProvider =
    AutoDisposeFutureProvider<List<Warranty>>.internal(
      activeWarranties,
      name: r'activeWarrantiesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$activeWarrantiesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ActiveWarrantiesRef = AutoDisposeFutureProviderRef<List<Warranty>>;
String _$expiredWarrantiesHash() => r'76181d3fba1fdbbd94f98c501b8fe7d62593d6df';

/// Provider للحصول على الضمانات منتهية الصلاحية
///
/// Copied from [expiredWarranties].
@ProviderFor(expiredWarranties)
final expiredWarrantiesProvider =
    AutoDisposeFutureProvider<List<Warranty>>.internal(
      expiredWarranties,
      name: r'expiredWarrantiesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$expiredWarrantiesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ExpiredWarrantiesRef = AutoDisposeFutureProviderRef<List<Warranty>>;
String _$warrantyManagerHash() => r'92d09b9ba7915b6cfcfbca59d7668852c16e4ee0';

/// See also [WarrantyManager].
@ProviderFor(WarrantyManager)
final warrantyManagerProvider =
    AutoDisposeAsyncNotifierProvider<WarrantyManager, List<Warranty>>.internal(
      WarrantyManager.new,
      name: r'warrantyManagerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$warrantyManagerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$WarrantyManager = AutoDisposeAsyncNotifier<List<Warranty>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
