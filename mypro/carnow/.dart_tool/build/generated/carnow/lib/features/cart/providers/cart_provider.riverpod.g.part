// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$cartHash() => r'c585f5fe8a37f107971b55a2beedc213bb4c54e5';

/// See also [Cart].
@ProviderFor(Cart)
final cartProvider =
    AutoDisposeAsyncNotifierProvider<
      Cart,
      List<CompleteCartItemModel>
    >.internal(
      Cart.new,
      name: r'cartProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$cartHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$Cart = AutoDisposeAsyncNotifier<List<CompleteCartItemModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
