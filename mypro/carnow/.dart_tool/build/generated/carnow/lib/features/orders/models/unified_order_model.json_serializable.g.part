// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_OrderItem _$OrderItemFromJson(Map<String, dynamic> json) => _OrderItem(
  id: json['id'] as String,
  productId: json['product_id'] as String,
  productName: json['product_name'] as String,
  productSku: json['product_sku'] as String,
  productImageUrl: json['product_image_url'] as String?,
  quantity: (json['quantity'] as num).toInt(),
  unitPrice: (json['unit_price'] as num).toDouble(),
  totalPrice: (json['total_price'] as num).toDouble(),
  notes: json['notes'] as String?,
  specifications: json['specifications'] as Map<String, dynamic>?,
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
);

Map<String, dynamic> _$OrderItemToJson(_OrderItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'product_id': instance.productId,
      'product_name': instance.productName,
      'product_sku': instance.productSku,
      'product_image_url': instance.productImageUrl,
      'quantity': instance.quantity,
      'unit_price': instance.unitPrice,
      'total_price': instance.totalPrice,
      'notes': instance.notes,
      'specifications': instance.specifications,
    };

_ShippingAddress _$ShippingAddressFromJson(Map<String, dynamic> json) =>
    _ShippingAddress(
      fullName: json['full_name'] as String,
      phoneNumber: json['phone_number'] as String,
      addressLine1: json['address_line1'] as String,
      addressLine2: json['address_line2'] as String?,
      city: json['city'] as String,
      state: json['state'] as String,
      postalCode: json['postal_code'] as String,
      country: json['country'] as String,
      specialInstructions: json['special_instructions'] as String?,
      isDefault: json['is_default'] as bool?,
    );

Map<String, dynamic> _$ShippingAddressToJson(_ShippingAddress instance) =>
    <String, dynamic>{
      'full_name': instance.fullName,
      'phone_number': instance.phoneNumber,
      'address_line1': instance.addressLine1,
      'address_line2': instance.addressLine2,
      'city': instance.city,
      'state': instance.state,
      'postal_code': instance.postalCode,
      'country': instance.country,
      'special_instructions': instance.specialInstructions,
      'is_default': instance.isDefault,
    };

_OrderStatusHistory _$OrderStatusHistoryFromJson(Map<String, dynamic> json) =>
    _OrderStatusHistory(
      status: $enumDecode(_$OrderStatusEnumMap, json['status']),
      timestamp: DateTime.parse(json['timestamp'] as String),
      notes: json['notes'] as String?,
      updatedBy: json['updated_by'] as String?,
    );

Map<String, dynamic> _$OrderStatusHistoryToJson(_OrderStatusHistory instance) =>
    <String, dynamic>{
      'status': _$OrderStatusEnumMap[instance.status]!,
      'timestamp': instance.timestamp.toIso8601String(),
      'notes': instance.notes,
      'updated_by': instance.updatedBy,
    };

const _$OrderStatusEnumMap = {
  OrderStatus.pending: 'pending',
  OrderStatus.confirmed: 'confirmed',
  OrderStatus.processing: 'processing',
  OrderStatus.shipped: 'shipped',
  OrderStatus.delivered: 'delivered',
  OrderStatus.cancelled: 'cancelled',
  OrderStatus.refunded: 'refunded',
  OrderStatus.returned: 'returned',
};

_PaymentInfo _$PaymentInfoFromJson(Map<String, dynamic> json) => _PaymentInfo(
  method: json['method'] as String,
  transactionId: json['transaction_id'] as String?,
  paymentGateway: json['payment_gateway'] as String?,
  paidAmount: (json['paid_amount'] as num?)?.toDouble(),
  currency: json['currency'] as String?,
  paidAt: json['paid_at'] == null
      ? null
      : DateTime.parse(json['paid_at'] as String),
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$PaymentInfoToJson(_PaymentInfo instance) =>
    <String, dynamic>{
      'method': instance.method,
      'transaction_id': instance.transactionId,
      'payment_gateway': instance.paymentGateway,
      'paid_amount': instance.paidAmount,
      'currency': instance.currency,
      'paid_at': instance.paidAt?.toIso8601String(),
      'metadata': instance.metadata,
    };

_UnifiedOrder _$UnifiedOrderFromJson(Map<String, dynamic> json) =>
    _UnifiedOrder(
      id: json['id'] as String,
      orderNumber: json['order_number'] as String,
      displayName: json['display_name'] as String,
      orderType: $enumDecode(_$OrderTypeEnumMap, json['order_type']),
      status: $enumDecode(_$OrderStatusEnumMap, json['status']),
      priority: $enumDecode(_$OrderPriorityEnumMap, json['priority']),
      buyerId: json['buyer_id'] as String,
      sellerId: json['seller_id'] as String?,
      buyerName: json['buyer_name'] as String?,
      buyerEmail: json['buyer_email'] as String?,
      sellerName: json['seller_name'] as String?,
      sellerEmail: json['seller_email'] as String?,
      items: (json['items'] as List<dynamic>)
          .map((e) => OrderItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      shippingAddress: ShippingAddress.fromJson(
        json['shipping_address'] as Map<String, dynamic>,
      ),
      paymentInfo: json['payment_info'] == null
          ? null
          : PaymentInfo.fromJson(json['payment_info'] as Map<String, dynamic>),
      subtotal: (json['subtotal'] as num).toDouble(),
      taxAmount: (json['tax_amount'] as num).toDouble(),
      shippingCost: (json['shipping_cost'] as num).toDouble(),
      totalAmount: (json['total_amount'] as num).toDouble(),
      currency: json['currency'] as String,
      trackingNumber: json['tracking_number'] as String?,
      notes: json['notes'] as String?,
      cancellationReason: json['cancellation_reason'] as String?,
      estimatedDeliveryDate: json['estimated_delivery_date'] == null
          ? null
          : DateTime.parse(json['estimated_delivery_date'] as String),
      actualDeliveryDate: json['actual_delivery_date'] == null
          ? null
          : DateTime.parse(json['actual_delivery_date'] as String),
      statusHistory: (json['status_history'] as List<dynamic>?)
          ?.map((e) => OrderStatusHistory.fromJson(e as Map<String, dynamic>))
          .toList(),
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      isDeleted: json['is_deleted'] as bool?,
    );

Map<String, dynamic> _$UnifiedOrderToJson(
  _UnifiedOrder instance,
) => <String, dynamic>{
  'id': instance.id,
  'order_number': instance.orderNumber,
  'display_name': instance.displayName,
  'order_type': _$OrderTypeEnumMap[instance.orderType]!,
  'status': _$OrderStatusEnumMap[instance.status]!,
  'priority': _$OrderPriorityEnumMap[instance.priority]!,
  'buyer_id': instance.buyerId,
  'seller_id': instance.sellerId,
  'buyer_name': instance.buyerName,
  'buyer_email': instance.buyerEmail,
  'seller_name': instance.sellerName,
  'seller_email': instance.sellerEmail,
  'items': instance.items,
  'shipping_address': instance.shippingAddress,
  'payment_info': instance.paymentInfo,
  'subtotal': instance.subtotal,
  'tax_amount': instance.taxAmount,
  'shipping_cost': instance.shippingCost,
  'total_amount': instance.totalAmount,
  'currency': instance.currency,
  'tracking_number': instance.trackingNumber,
  'notes': instance.notes,
  'cancellation_reason': instance.cancellationReason,
  'estimated_delivery_date': instance.estimatedDeliveryDate?.toIso8601String(),
  'actual_delivery_date': instance.actualDeliveryDate?.toIso8601String(),
  'status_history': instance.statusHistory,
  'metadata': instance.metadata,
};

const _$OrderTypeEnumMap = {
  OrderType.autoParts: 'auto_parts',
  OrderType.clothing: 'clothing',
  OrderType.electronics: 'electronics',
  OrderType.homeGarden: 'home_garden',
  OrderType.sports: 'sports',
  OrderType.books: 'books',
  OrderType.general: 'general',
};

const _$OrderPriorityEnumMap = {
  OrderPriority.low: 'low',
  OrderPriority.normal: 'normal',
  OrderPriority.high: 'high',
  OrderPriority.urgent: 'urgent',
};
