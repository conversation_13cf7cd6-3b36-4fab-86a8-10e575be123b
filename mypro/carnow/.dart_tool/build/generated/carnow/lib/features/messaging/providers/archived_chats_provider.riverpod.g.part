// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$archivedChatsHash() => r'0e78c1e958bcfab151b1f4e8e15c6b9c803baa4a';

/// مزود المحادثات المؤرشفة
///
/// Copied from [archivedChats].
@ProviderFor(archivedChats)
final archivedChatsProvider =
    AutoDisposeFutureProvider<List<ChatModel>>.internal(
      archivedChats,
      name: r'archivedChatsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$archivedChatsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ArchivedChatsRef = AutoDisposeFutureProviderRef<List<ChatModel>>;
String _$archivedChatsManagerHash() =>
    r'f6a100531a23f66f2fa073fca1cae6bcfb9beb74';

/// مزود إدارة المحادثات المؤرشفة
///
/// Copied from [ArchivedChatsManager].
@ProviderFor(ArchivedChatsManager)
final archivedChatsManagerProvider =
    AutoDisposeNotifierProvider<
      ArchivedChatsManager,
      AsyncValue<void>
    >.internal(
      ArchivedChatsManager.new,
      name: r'archivedChatsManagerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$archivedChatsManagerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$ArchivedChatsManager = AutoDisposeNotifier<AsyncValue<void>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
