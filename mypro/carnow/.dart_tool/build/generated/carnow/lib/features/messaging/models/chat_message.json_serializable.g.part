// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatMessage _$ChatMessageFromJson(Map<String, dynamic> json) => ChatMessage(
  id: json['id'] as String,
  conversationId: json['conversation_id'] as String,
  senderId: json['sender_id'] as String,
  content: json['content'] as String,
  timestamp: DateTime.parse(json['timestamp'] as String),
  type: json['type'] as String? ?? 'text',
  status: json['status'] as String? ?? 'sent',
  replyToId: json['reply_to_id'] as String?,
  attachments: (json['attachments'] as List<dynamic>?)
      ?.map((e) => e as Map<String, dynamic>)
      .toList(),
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
  isDeleted: json['is_deleted'] as bool? ?? false,
);

Map<String, dynamic> _$ChatMessageToJson(ChatMessage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'conversation_id': instance.conversationId,
      'sender_id': instance.senderId,
      'content': instance.content,
      'timestamp': instance.timestamp.toIso8601String(),
      'type': instance.type,
      'status': instance.status,
      'reply_to_id': instance.replyToId,
      'attachments': instance.attachments,
      'metadata': instance.metadata,
      'is_deleted': instance.isDeleted,
    };
