// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SellerNotification _$SellerNotificationFromJson(Map<String, dynamic> json) =>
    _SellerNotification(
      id: json['id'] as String,
      sellerId: json['seller_id'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      notificationType: json['notification_type'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      relatedId: json['related_id'] as String?,
      readAt: json['read_at'] == null
          ? null
          : DateTime.parse(json['read_at'] as String),
    );

Map<String, dynamic> _$SellerNotificationToJson(_SellerNotification instance) =>
    <String, dynamic>{
      'id': instance.id,
      'seller_id': instance.sellerId,
      'title': instance.title,
      'message': instance.message,
      'notification_type': instance.notificationType,
      'created_at': instance.createdAt.toIso8601String(),
      'related_id': instance.relatedId,
      'read_at': instance.readAt?.toIso8601String(),
    };
