// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$categoryBySlugHash() => r'00653f20cf47f97fc46226630f5e7c342de8244c';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider for accessing a specific category by slug
///
/// Copied from [categoryBySlug].
@ProviderFor(categoryBySlug)
const categoryBySlugProvider = CategoryBySlugFamily();

/// Provider for accessing a specific category by slug
///
/// Copied from [categoryBySlug].
class CategoryBySlugFamily extends Family<AsyncValue<CategoryModel>> {
  /// Provider for accessing a specific category by slug
  ///
  /// Copied from [categoryBySlug].
  const CategoryBySlugFamily();

  /// Provider for accessing a specific category by slug
  ///
  /// Copied from [categoryBySlug].
  CategoryBySlugProvider call(String slug) {
    return CategoryBySlugProvider(slug);
  }

  @override
  CategoryBySlugProvider getProviderOverride(
    covariant CategoryBySlugProvider provider,
  ) {
    return call(provider.slug);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'categoryBySlugProvider';
}

/// Provider for accessing a specific category by slug
///
/// Copied from [categoryBySlug].
class CategoryBySlugProvider extends AutoDisposeFutureProvider<CategoryModel> {
  /// Provider for accessing a specific category by slug
  ///
  /// Copied from [categoryBySlug].
  CategoryBySlugProvider(String slug)
    : this._internal(
        (ref) => categoryBySlug(ref as CategoryBySlugRef, slug),
        from: categoryBySlugProvider,
        name: r'categoryBySlugProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$categoryBySlugHash,
        dependencies: CategoryBySlugFamily._dependencies,
        allTransitiveDependencies:
            CategoryBySlugFamily._allTransitiveDependencies,
        slug: slug,
      );

  CategoryBySlugProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.slug,
  }) : super.internal();

  final String slug;

  @override
  Override overrideWith(
    FutureOr<CategoryModel> Function(CategoryBySlugRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CategoryBySlugProvider._internal(
        (ref) => create(ref as CategoryBySlugRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        slug: slug,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<CategoryModel> createElement() {
    return _CategoryBySlugProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CategoryBySlugProvider && other.slug == slug;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, slug.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CategoryBySlugRef on AutoDisposeFutureProviderRef<CategoryModel> {
  /// The parameter `slug` of this provider.
  String get slug;
}

class _CategoryBySlugProviderElement
    extends AutoDisposeFutureProviderElement<CategoryModel>
    with CategoryBySlugRef {
  _CategoryBySlugProviderElement(super.provider);

  @override
  String get slug => (origin as CategoryBySlugProvider).slug;
}

String _$childCategoriesHash() => r'30489b89976e2794b502b0a6217a903b88242f9f';

/// Provider for accessing child categories of a parent
///
/// Copied from [childCategories].
@ProviderFor(childCategories)
const childCategoriesProvider = ChildCategoriesFamily();

/// Provider for accessing child categories of a parent
///
/// Copied from [childCategories].
class ChildCategoriesFamily extends Family<AsyncValue<List<CategoryModel>>> {
  /// Provider for accessing child categories of a parent
  ///
  /// Copied from [childCategories].
  const ChildCategoriesFamily();

  /// Provider for accessing child categories of a parent
  ///
  /// Copied from [childCategories].
  ChildCategoriesProvider call(String parentId) {
    return ChildCategoriesProvider(parentId);
  }

  @override
  ChildCategoriesProvider getProviderOverride(
    covariant ChildCategoriesProvider provider,
  ) {
    return call(provider.parentId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'childCategoriesProvider';
}

/// Provider for accessing child categories of a parent
///
/// Copied from [childCategories].
class ChildCategoriesProvider
    extends AutoDisposeFutureProvider<List<CategoryModel>> {
  /// Provider for accessing child categories of a parent
  ///
  /// Copied from [childCategories].
  ChildCategoriesProvider(String parentId)
    : this._internal(
        (ref) => childCategories(ref as ChildCategoriesRef, parentId),
        from: childCategoriesProvider,
        name: r'childCategoriesProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$childCategoriesHash,
        dependencies: ChildCategoriesFamily._dependencies,
        allTransitiveDependencies:
            ChildCategoriesFamily._allTransitiveDependencies,
        parentId: parentId,
      );

  ChildCategoriesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.parentId,
  }) : super.internal();

  final String parentId;

  @override
  Override overrideWith(
    FutureOr<List<CategoryModel>> Function(ChildCategoriesRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ChildCategoriesProvider._internal(
        (ref) => create(ref as ChildCategoriesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        parentId: parentId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<CategoryModel>> createElement() {
    return _ChildCategoriesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ChildCategoriesProvider && other.parentId == parentId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, parentId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ChildCategoriesRef on AutoDisposeFutureProviderRef<List<CategoryModel>> {
  /// The parameter `parentId` of this provider.
  String get parentId;
}

class _ChildCategoriesProviderElement
    extends AutoDisposeFutureProviderElement<List<CategoryModel>>
    with ChildCategoriesRef {
  _ChildCategoriesProviderElement(super.provider);

  @override
  String get parentId => (origin as ChildCategoriesProvider).parentId;
}

String _$topLevelCategoriesHash() =>
    r'2cc78f5d7d92a7757bc4559309214b6c41426e7d';

/// Provider for accessing top-level categories
///
/// Copied from [topLevelCategories].
@ProviderFor(topLevelCategories)
final topLevelCategoriesProvider =
    AutoDisposeFutureProvider<List<CategoryModel>>.internal(
      topLevelCategories,
      name: r'topLevelCategoriesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$topLevelCategoriesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef TopLevelCategoriesRef =
    AutoDisposeFutureProviderRef<List<CategoryModel>>;
String _$categoryHash() => r'd7d8ce9dde386622362438f4fbb9cc72c5990bbf';

/// Provider for accessing all categories with caching
///
/// Copied from [Category].
@ProviderFor(Category)
final categoryProvider =
    AsyncNotifierProvider<Category, List<CategoryModel>>.internal(
      Category.new,
      name: r'categoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$categoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$Category = AsyncNotifier<List<CategoryModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
