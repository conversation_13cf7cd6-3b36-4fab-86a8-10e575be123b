// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_BidModel _$BidModelFromJson(Map<String, dynamic> json) => _BidModel(
  id: json['id'] as String,
  productId: json['product_id'] as String,
  bidderId: json['bidder_id'] as String,
  bidAmount: (json['bid_amount'] as num).toDouble(),
  status: $enumDecode(_$BidStatusEnumMap, json['status']),
  notes: json['notes'] as String?,
  isAutomaticBid: json['is_automatic_bid'] as bool? ?? false,
  maxBidAmount: (json['max_bid_amount'] as num?)?.toDouble(),
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
);

Map<String, dynamic> _$BidModelToJson(_BidModel instance) => <String, dynamic>{
  'id': instance.id,
  'product_id': instance.productId,
  'bidder_id': instance.bidderId,
  'bid_amount': instance.bidAmount,
  'status': _$BidStatusEnumMap[instance.status]!,
  'notes': instance.notes,
  'is_automatic_bid': instance.isAutomaticBid,
  'max_bid_amount': instance.maxBidAmount,
};

const _$BidStatusEnumMap = {
  BidStatus.active: 'active',
  BidStatus.outbid: 'outbid',
  BidStatus.winning: 'winning',
  BidStatus.won: 'won',
  BidStatus.cancelled: 'cancelled',
};

_CompleteBidModel _$CompleteBidModelFromJson(Map<String, dynamic> json) =>
    _CompleteBidModel(
      bid: BidModel.fromJson(json['bid'] as Map<String, dynamic>),
      productName: json['product_name'] as String,
      productImageUrl: json['product_image_url'] as String?,
      bidderName: json['bidder_name'] as String,
      bidderAvatar: json['bidder_avatar'] as String?,
      sellerId: json['seller_id'] as String?,
      sellerName: json['seller_name'] as String?,
    );

Map<String, dynamic> _$CompleteBidModelToJson(_CompleteBidModel instance) =>
    <String, dynamic>{
      'bid': instance.bid.toJson(),
      'product_name': instance.productName,
      'product_image_url': instance.productImageUrl,
      'bidder_name': instance.bidderName,
      'bidder_avatar': instance.bidderAvatar,
      'seller_id': instance.sellerId,
      'seller_name': instance.sellerName,
    };
