// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Warranty _$WarrantyFromJson(Map<String, dynamic> json) => _Warranty(
  id: json['id'] as String,
  productId: json['productId'] as String,
  sellerId: json['sellerId'] as String,
  buyerId: json['buyerId'] as String,
  type: $enumDecode(_$WarrantyTypeEnumMap, json['type']),
  duration: Duration(microseconds: (json['duration'] as num).toInt()),
  startDate: DateTime.parse(json['startDate'] as String),
  endDate: DateTime.parse(json['endDate'] as String),
  status: $enumDecode(_$WarrantyStatusEnumMap, json['status']),
  terms: json['terms'] as Map<String, dynamic>,
  coveredIssues: (json['coveredIssues'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  excludedIssues: (json['excludedIssues'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  description: json['description'] as String?,
  certificateNumber: json['certificateNumber'] as String?,
  claims: (json['claims'] as List<dynamic>?)
      ?.map((e) => WarrantyClaim.fromJson(e as Map<String, dynamic>))
      .toList(),
  metadata: json['metadata'] as Map<String, dynamic>?,
  createdAt: DateTime.parse(json['created_at'] as String),
  updatedAt: DateTime.parse(json['updated_at'] as String),
);

Map<String, dynamic> _$WarrantyToJson(_Warranty instance) => <String, dynamic>{
  'id': instance.id,
  'productId': instance.productId,
  'sellerId': instance.sellerId,
  'buyerId': instance.buyerId,
  'type': _$WarrantyTypeEnumMap[instance.type]!,
  'duration': instance.duration.inMicroseconds,
  'startDate': instance.startDate.toIso8601String(),
  'endDate': instance.endDate.toIso8601String(),
  'status': _$WarrantyStatusEnumMap[instance.status]!,
  'terms': instance.terms,
  'coveredIssues': instance.coveredIssues,
  'excludedIssues': instance.excludedIssues,
  'description': instance.description,
  'certificateNumber': instance.certificateNumber,
  'claims': instance.claims,
  'metadata': instance.metadata,
  'created_at': instance.createdAt.toIso8601String(),
  'updated_at': instance.updatedAt.toIso8601String(),
};

const _$WarrantyTypeEnumMap = {
  WarrantyType.manufacturer: 'manufacturer',
  WarrantyType.seller: 'seller',
  WarrantyType.extended: 'extended',
  WarrantyType.thirdParty: 'third_party',
};

const _$WarrantyStatusEnumMap = {
  WarrantyStatus.active: 'active',
  WarrantyStatus.expired: 'expired',
  WarrantyStatus.voided: 'voided',
  WarrantyStatus.suspended: 'suspended',
};

_WarrantyClaim _$WarrantyClaimFromJson(Map<String, dynamic> json) =>
    _WarrantyClaim(
      id: json['id'] as String,
      warrantyId: json['warrantyId'] as String,
      claimantId: json['claimantId'] as String,
      issueDescription: json['issueDescription'] as String,
      status: $enumDecode(_$WarrantyClaimStatusEnumMap, json['status']),
      submittedAt: DateTime.parse(json['submittedAt'] as String),
      resolvedAt: json['resolvedAt'] == null
          ? null
          : DateTime.parse(json['resolvedAt'] as String),
      resolution: json['resolution'] as String?,
      supportingDocuments: (json['supportingDocuments'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      technicianNotes: json['technicianNotes'] as String?,
      estimatedCost: (json['estimatedCost'] as num?)?.toDouble(),
      actualCost: (json['actualCost'] as num?)?.toDouble(),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$WarrantyClaimToJson(_WarrantyClaim instance) =>
    <String, dynamic>{
      'id': instance.id,
      'warrantyId': instance.warrantyId,
      'claimantId': instance.claimantId,
      'issueDescription': instance.issueDescription,
      'status': _$WarrantyClaimStatusEnumMap[instance.status]!,
      'submittedAt': instance.submittedAt.toIso8601String(),
      'resolvedAt': instance.resolvedAt?.toIso8601String(),
      'resolution': instance.resolution,
      'supportingDocuments': instance.supportingDocuments,
      'technicianNotes': instance.technicianNotes,
      'estimatedCost': instance.estimatedCost,
      'actualCost': instance.actualCost,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

const _$WarrantyClaimStatusEnumMap = {
  WarrantyClaimStatus.submitted: 'submitted',
  WarrantyClaimStatus.underReview: 'under_review',
  WarrantyClaimStatus.approved: 'approved',
  WarrantyClaimStatus.rejected: 'rejected',
  WarrantyClaimStatus.completed: 'completed',
  WarrantyClaimStatus.cancelled: 'cancelled',
};
