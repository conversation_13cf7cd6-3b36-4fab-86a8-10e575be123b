// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$compatiblePartsHash() => r'f0e90b1301a49a57b1747d91a3523f36d19e5ad0';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Compatible Parts Provider
/// Following Forever Plan: Flutter → Go API → Supabase
///
/// Copied from [compatibleParts].
@ProviderFor(compatibleParts)
const compatiblePartsProvider = CompatiblePartsFamily();

/// Compatible Parts Provider
/// Following Forever Plan: Flutter → Go API → Supabase
///
/// Copied from [compatibleParts].
class CompatiblePartsFamily extends Family<AsyncValue<List<ProductModel>>> {
  /// Compatible Parts Provider
  /// Following Forever Plan: Flutter → Go API → Supabase
  ///
  /// Copied from [compatibleParts].
  const CompatiblePartsFamily();

  /// Compatible Parts Provider
  /// Following Forever Plan: Flutter → Go API → Supabase
  ///
  /// Copied from [compatibleParts].
  CompatiblePartsProvider call(String vehicleId, String category) {
    return CompatiblePartsProvider(vehicleId, category);
  }

  @override
  CompatiblePartsProvider getProviderOverride(
    covariant CompatiblePartsProvider provider,
  ) {
    return call(provider.vehicleId, provider.category);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'compatiblePartsProvider';
}

/// Compatible Parts Provider
/// Following Forever Plan: Flutter → Go API → Supabase
///
/// Copied from [compatibleParts].
class CompatiblePartsProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// Compatible Parts Provider
  /// Following Forever Plan: Flutter → Go API → Supabase
  ///
  /// Copied from [compatibleParts].
  CompatiblePartsProvider(String vehicleId, String category)
    : this._internal(
        (ref) =>
            compatibleParts(ref as CompatiblePartsRef, vehicleId, category),
        from: compatiblePartsProvider,
        name: r'compatiblePartsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$compatiblePartsHash,
        dependencies: CompatiblePartsFamily._dependencies,
        allTransitiveDependencies:
            CompatiblePartsFamily._allTransitiveDependencies,
        vehicleId: vehicleId,
        category: category,
      );

  CompatiblePartsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.vehicleId,
    required this.category,
  }) : super.internal();

  final String vehicleId;
  final String category;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(CompatiblePartsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CompatiblePartsProvider._internal(
        (ref) => create(ref as CompatiblePartsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        vehicleId: vehicleId,
        category: category,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _CompatiblePartsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CompatiblePartsProvider &&
        other.vehicleId == vehicleId &&
        other.category == category;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, vehicleId.hashCode);
    hash = _SystemHash.combine(hash, category.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CompatiblePartsRef on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `vehicleId` of this provider.
  String get vehicleId;

  /// The parameter `category` of this provider.
  String get category;
}

class _CompatiblePartsProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with CompatiblePartsRef {
  _CompatiblePartsProviderElement(super.provider);

  @override
  String get vehicleId => (origin as CompatiblePartsProvider).vehicleId;
  @override
  String get category => (origin as CompatiblePartsProvider).category;
}

String _$vehicleHash() => r'e2c49495d99a1f87c8b9e6464cf2124051652956';

/// See also [vehicle].
@ProviderFor(vehicle)
const vehicleProvider = VehicleFamily();

/// See also [vehicle].
class VehicleFamily extends Family<AsyncValue<CarModel?>> {
  /// See also [vehicle].
  const VehicleFamily();

  /// See also [vehicle].
  VehicleProvider call(String vehicleId) {
    return VehicleProvider(vehicleId);
  }

  @override
  VehicleProvider getProviderOverride(covariant VehicleProvider provider) {
    return call(provider.vehicleId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'vehicleProvider';
}

/// See also [vehicle].
class VehicleProvider extends AutoDisposeFutureProvider<CarModel?> {
  /// See also [vehicle].
  VehicleProvider(String vehicleId)
    : this._internal(
        (ref) => vehicle(ref as VehicleRef, vehicleId),
        from: vehicleProvider,
        name: r'vehicleProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$vehicleHash,
        dependencies: VehicleFamily._dependencies,
        allTransitiveDependencies: VehicleFamily._allTransitiveDependencies,
        vehicleId: vehicleId,
      );

  VehicleProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.vehicleId,
  }) : super.internal();

  final String vehicleId;

  @override
  Override overrideWith(
    FutureOr<CarModel?> Function(VehicleRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: VehicleProvider._internal(
        (ref) => create(ref as VehicleRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        vehicleId: vehicleId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<CarModel?> createElement() {
    return _VehicleProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is VehicleProvider && other.vehicleId == vehicleId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, vehicleId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin VehicleRef on AutoDisposeFutureProviderRef<CarModel?> {
  /// The parameter `vehicleId` of this provider.
  String get vehicleId;
}

class _VehicleProviderElement
    extends AutoDisposeFutureProviderElement<CarModel?>
    with VehicleRef {
  _VehicleProviderElement(super.provider);

  @override
  String get vehicleId => (origin as VehicleProvider).vehicleId;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
