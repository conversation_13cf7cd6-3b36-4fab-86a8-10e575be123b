// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$categoryProductsHash() => r'b1e1fbe0ca26d56dbb737848d1dc8a6ca9096c5c';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// مزود يقوم بتحميل المنتجات حسب الفئة والمعايير الأخرى
/// يدعم الخطة التوسعية للتطبيق بالسماح بتصفية المنتجات حسب الفئات المتعددة
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [categoryProducts].
@ProviderFor(categoryProducts)
const categoryProductsProvider = CategoryProductsFamily();

/// مزود يقوم بتحميل المنتجات حسب الفئة والمعايير الأخرى
/// يدعم الخطة التوسعية للتطبيق بالسماح بتصفية المنتجات حسب الفئات المتعددة
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [categoryProducts].
class CategoryProductsFamily extends Family<AsyncValue<List<ProductModel>>> {
  /// مزود يقوم بتحميل المنتجات حسب الفئة والمعايير الأخرى
  /// يدعم الخطة التوسعية للتطبيق بالسماح بتصفية المنتجات حسب الفئات المتعددة
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [categoryProducts].
  const CategoryProductsFamily();

  /// مزود يقوم بتحميل المنتجات حسب الفئة والمعايير الأخرى
  /// يدعم الخطة التوسعية للتطبيق بالسماح بتصفية المنتجات حسب الفئات المتعددة
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [categoryProducts].
  CategoryProductsProvider call({
    String? categorySlug,
    bool featured = false,
    String? condition,
    int limit = 10,
  }) {
    return CategoryProductsProvider(
      categorySlug: categorySlug,
      featured: featured,
      condition: condition,
      limit: limit,
    );
  }

  @override
  CategoryProductsProvider getProviderOverride(
    covariant CategoryProductsProvider provider,
  ) {
    return call(
      categorySlug: provider.categorySlug,
      featured: provider.featured,
      condition: provider.condition,
      limit: provider.limit,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'categoryProductsProvider';
}

/// مزود يقوم بتحميل المنتجات حسب الفئة والمعايير الأخرى
/// يدعم الخطة التوسعية للتطبيق بالسماح بتصفية المنتجات حسب الفئات المتعددة
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [categoryProducts].
class CategoryProductsProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// مزود يقوم بتحميل المنتجات حسب الفئة والمعايير الأخرى
  /// يدعم الخطة التوسعية للتطبيق بالسماح بتصفية المنتجات حسب الفئات المتعددة
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [categoryProducts].
  CategoryProductsProvider({
    String? categorySlug,
    bool featured = false,
    String? condition,
    int limit = 10,
  }) : this._internal(
         (ref) => categoryProducts(
           ref as CategoryProductsRef,
           categorySlug: categorySlug,
           featured: featured,
           condition: condition,
           limit: limit,
         ),
         from: categoryProductsProvider,
         name: r'categoryProductsProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$categoryProductsHash,
         dependencies: CategoryProductsFamily._dependencies,
         allTransitiveDependencies:
             CategoryProductsFamily._allTransitiveDependencies,
         categorySlug: categorySlug,
         featured: featured,
         condition: condition,
         limit: limit,
       );

  CategoryProductsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categorySlug,
    required this.featured,
    required this.condition,
    required this.limit,
  }) : super.internal();

  final String? categorySlug;
  final bool featured;
  final String? condition;
  final int limit;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(CategoryProductsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CategoryProductsProvider._internal(
        (ref) => create(ref as CategoryProductsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categorySlug: categorySlug,
        featured: featured,
        condition: condition,
        limit: limit,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _CategoryProductsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CategoryProductsProvider &&
        other.categorySlug == categorySlug &&
        other.featured == featured &&
        other.condition == condition &&
        other.limit == limit;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categorySlug.hashCode);
    hash = _SystemHash.combine(hash, featured.hashCode);
    hash = _SystemHash.combine(hash, condition.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CategoryProductsRef on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `categorySlug` of this provider.
  String? get categorySlug;

  /// The parameter `featured` of this provider.
  bool get featured;

  /// The parameter `condition` of this provider.
  String? get condition;

  /// The parameter `limit` of this provider.
  int get limit;
}

class _CategoryProductsProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with CategoryProductsRef {
  _CategoryProductsProviderElement(super.provider);

  @override
  String? get categorySlug => (origin as CategoryProductsProvider).categorySlug;
  @override
  bool get featured => (origin as CategoryProductsProvider).featured;
  @override
  String? get condition => (origin as CategoryProductsProvider).condition;
  @override
  int get limit => (origin as CategoryProductsProvider).limit;
}

String _$popularProductsHash() => r'19fb37710f78aff3a988e1ea9249fb9d8d20dedd';

/// مزود للمنتجات الشائعة (بدون تحديد فئة)
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [popularProducts].
@ProviderFor(popularProducts)
const popularProductsProvider = PopularProductsFamily();

/// مزود للمنتجات الشائعة (بدون تحديد فئة)
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [popularProducts].
class PopularProductsFamily extends Family<AsyncValue<List<ProductModel>>> {
  /// مزود للمنتجات الشائعة (بدون تحديد فئة)
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [popularProducts].
  const PopularProductsFamily();

  /// مزود للمنتجات الشائعة (بدون تحديد فئة)
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [popularProducts].
  PopularProductsProvider call({int limit = 10}) {
    return PopularProductsProvider(limit: limit);
  }

  @override
  PopularProductsProvider getProviderOverride(
    covariant PopularProductsProvider provider,
  ) {
    return call(limit: provider.limit);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'popularProductsProvider';
}

/// مزود للمنتجات الشائعة (بدون تحديد فئة)
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [popularProducts].
class PopularProductsProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// مزود للمنتجات الشائعة (بدون تحديد فئة)
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [popularProducts].
  PopularProductsProvider({int limit = 10})
    : this._internal(
        (ref) => popularProducts(ref as PopularProductsRef, limit: limit),
        from: popularProductsProvider,
        name: r'popularProductsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$popularProductsHash,
        dependencies: PopularProductsFamily._dependencies,
        allTransitiveDependencies:
            PopularProductsFamily._allTransitiveDependencies,
        limit: limit,
      );

  PopularProductsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.limit,
  }) : super.internal();

  final int limit;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(PopularProductsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: PopularProductsProvider._internal(
        (ref) => create(ref as PopularProductsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        limit: limit,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _PopularProductsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is PopularProductsProvider && other.limit == limit;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin PopularProductsRef on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `limit` of this provider.
  int get limit;
}

class _PopularProductsProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with PopularProductsRef {
  _PopularProductsProviderElement(super.provider);

  @override
  int get limit => (origin as PopularProductsProvider).limit;
}

String _$searchProductsHash() => r'673c11631cd2b09ad68bc8730a1dae58cd64252f';

/// مزود للبحث في المنتجات
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [searchProducts].
@ProviderFor(searchProducts)
const searchProductsProvider = SearchProductsFamily();

/// مزود للبحث في المنتجات
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [searchProducts].
class SearchProductsFamily extends Family<AsyncValue<List<ProductModel>>> {
  /// مزود للبحث في المنتجات
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [searchProducts].
  const SearchProductsFamily();

  /// مزود للبحث في المنتجات
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [searchProducts].
  SearchProductsProvider call({
    required String query,
    String? categorySlug,
    int limit = 20,
  }) {
    return SearchProductsProvider(
      query: query,
      categorySlug: categorySlug,
      limit: limit,
    );
  }

  @override
  SearchProductsProvider getProviderOverride(
    covariant SearchProductsProvider provider,
  ) {
    return call(
      query: provider.query,
      categorySlug: provider.categorySlug,
      limit: provider.limit,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'searchProductsProvider';
}

/// مزود للبحث في المنتجات
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [searchProducts].
class SearchProductsProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// مزود للبحث في المنتجات
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [searchProducts].
  SearchProductsProvider({
    required String query,
    String? categorySlug,
    int limit = 20,
  }) : this._internal(
         (ref) => searchProducts(
           ref as SearchProductsRef,
           query: query,
           categorySlug: categorySlug,
           limit: limit,
         ),
         from: searchProductsProvider,
         name: r'searchProductsProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$searchProductsHash,
         dependencies: SearchProductsFamily._dependencies,
         allTransitiveDependencies:
             SearchProductsFamily._allTransitiveDependencies,
         query: query,
         categorySlug: categorySlug,
         limit: limit,
       );

  SearchProductsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
    required this.categorySlug,
    required this.limit,
  }) : super.internal();

  final String query;
  final String? categorySlug;
  final int limit;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(SearchProductsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SearchProductsProvider._internal(
        (ref) => create(ref as SearchProductsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
        categorySlug: categorySlug,
        limit: limit,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _SearchProductsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SearchProductsProvider &&
        other.query == query &&
        other.categorySlug == categorySlug &&
        other.limit == limit;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);
    hash = _SystemHash.combine(hash, categorySlug.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SearchProductsRef on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `query` of this provider.
  String get query;

  /// The parameter `categorySlug` of this provider.
  String? get categorySlug;

  /// The parameter `limit` of this provider.
  int get limit;
}

class _SearchProductsProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with SearchProductsRef {
  _SearchProductsProviderElement(super.provider);

  @override
  String get query => (origin as SearchProductsProvider).query;
  @override
  String? get categorySlug => (origin as SearchProductsProvider).categorySlug;
  @override
  int get limit => (origin as SearchProductsProvider).limit;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
