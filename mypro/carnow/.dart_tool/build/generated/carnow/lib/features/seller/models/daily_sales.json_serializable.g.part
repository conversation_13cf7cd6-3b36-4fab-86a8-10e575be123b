// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_DailySales _$DailySalesFromJson(Map<String, dynamic> json) => _DailySales(
  date: json['date'] == null ? null : DateTime.parse(json['date'] as String),
  revenue: (json['revenue'] as num?)?.toDouble(),
  orders: (json['orders'] as num?)?.toInt(),
);

Map<String, dynamic> _$DailySalesToJson(_DailySales instance) =>
    <String, dynamic>{
      'date': instance.date?.toIso8601String(),
      'revenue': instance.revenue,
      'orders': instance.orders,
    };
