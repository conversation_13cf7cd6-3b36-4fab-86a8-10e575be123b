// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ModelYear _$ModelYearFromJson(Map<String, dynamic> json) => _ModelYear(
  id: (json['id'] as num).toInt(),
  modelId: (json['model_id'] as num).toInt(),
  year: (json['year'] as num).toInt(),
  isFacelift: json['is_facelift'] as bool? ?? false,
  faceliftDescription: json['facelift_description'] as String?,
  productionStartMonth: (json['production_start_month'] as num?)?.toInt(),
  productionEndMonth: (json['production_end_month'] as num?)?.toInt(),
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
  isDeleted: json['is_deleted'] as bool? ?? false,
);

Map<String, dynamic> _$ModelYearToJson(_ModelYear instance) =>
    <String, dynamic>{
      'id': instance.id,
      'model_id': instance.modelId,
      'year': instance.year,
      'is_facelift': instance.isFacelift,
      'facelift_description': instance.faceliftDescription,
      'production_start_month': instance.productionStartMonth,
      'production_end_month': instance.productionEndMonth,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'is_deleted': instance.isDeleted,
    };
