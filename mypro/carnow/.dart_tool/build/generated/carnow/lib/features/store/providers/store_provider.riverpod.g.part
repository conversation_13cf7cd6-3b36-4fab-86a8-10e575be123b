// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$storeCategoriesHash() => r'b1cceea6500c021ba4693fa80b239c71239e4fed';

/// Provider for store categories
///
/// Copied from [storeCategories].
@ProviderFor(storeCategories)
final storeCategoriesProvider =
    AutoDisposeFutureProvider<List<StoreCategoryModel>>.internal(
      storeCategories,
      name: r'storeCategoriesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$storeCategoriesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef StoreCategoriesRef =
    AutoDisposeFutureProviderRef<List<StoreCategoryModel>>;
String _$featuredStoresHash() => r'e2bdd349e31b5c91bf3e8565b6bab45bfb1d9690';

/// Provider for featured stores
///
/// Copied from [featuredStores].
@ProviderFor(featuredStores)
final featuredStoresProvider =
    AutoDisposeFutureProvider<List<StoreModel>>.internal(
      featuredStores,
      name: r'featuredStoresProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$featuredStoresHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FeaturedStoresRef = AutoDisposeFutureProviderRef<List<StoreModel>>;
String _$storeBySellerIDHash() => r'edd0566a766091ab9617f169461ad08ffa0d2c70';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider for store by seller ID
///
/// Copied from [storeBySellerID].
@ProviderFor(storeBySellerID)
const storeBySellerIDProvider = StoreBySellerIDFamily();

/// Provider for store by seller ID
///
/// Copied from [storeBySellerID].
class StoreBySellerIDFamily extends Family<AsyncValue<StoreModel?>> {
  /// Provider for store by seller ID
  ///
  /// Copied from [storeBySellerID].
  const StoreBySellerIDFamily();

  /// Provider for store by seller ID
  ///
  /// Copied from [storeBySellerID].
  StoreBySellerIDProvider call(int sellerId) {
    return StoreBySellerIDProvider(sellerId);
  }

  @override
  StoreBySellerIDProvider getProviderOverride(
    covariant StoreBySellerIDProvider provider,
  ) {
    return call(provider.sellerId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'storeBySellerIDProvider';
}

/// Provider for store by seller ID
///
/// Copied from [storeBySellerID].
class StoreBySellerIDProvider extends AutoDisposeFutureProvider<StoreModel?> {
  /// Provider for store by seller ID
  ///
  /// Copied from [storeBySellerID].
  StoreBySellerIDProvider(int sellerId)
    : this._internal(
        (ref) => storeBySellerID(ref as StoreBySellerIDRef, sellerId),
        from: storeBySellerIDProvider,
        name: r'storeBySellerIDProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$storeBySellerIDHash,
        dependencies: StoreBySellerIDFamily._dependencies,
        allTransitiveDependencies:
            StoreBySellerIDFamily._allTransitiveDependencies,
        sellerId: sellerId,
      );

  StoreBySellerIDProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.sellerId,
  }) : super.internal();

  final int sellerId;

  @override
  Override overrideWith(
    FutureOr<StoreModel?> Function(StoreBySellerIDRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: StoreBySellerIDProvider._internal(
        (ref) => create(ref as StoreBySellerIDRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        sellerId: sellerId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<StoreModel?> createElement() {
    return _StoreBySellerIDProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is StoreBySellerIDProvider && other.sellerId == sellerId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, sellerId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin StoreBySellerIDRef on AutoDisposeFutureProviderRef<StoreModel?> {
  /// The parameter `sellerId` of this provider.
  int get sellerId;
}

class _StoreBySellerIDProviderElement
    extends AutoDisposeFutureProviderElement<StoreModel?>
    with StoreBySellerIDRef {
  _StoreBySellerIDProviderElement(super.provider);

  @override
  int get sellerId => (origin as StoreBySellerIDProvider).sellerId;
}

String _$storeBySlugHash() => r'c60a836b6fa6f85f353eaccb9d07ad818e44a180';

/// Provider for store by slug
///
/// Copied from [storeBySlug].
@ProviderFor(storeBySlug)
const storeBySlugProvider = StoreBySlugFamily();

/// Provider for store by slug
///
/// Copied from [storeBySlug].
class StoreBySlugFamily extends Family<AsyncValue<StoreModel?>> {
  /// Provider for store by slug
  ///
  /// Copied from [storeBySlug].
  const StoreBySlugFamily();

  /// Provider for store by slug
  ///
  /// Copied from [storeBySlug].
  StoreBySlugProvider call(String slug) {
    return StoreBySlugProvider(slug);
  }

  @override
  StoreBySlugProvider getProviderOverride(
    covariant StoreBySlugProvider provider,
  ) {
    return call(provider.slug);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'storeBySlugProvider';
}

/// Provider for store by slug
///
/// Copied from [storeBySlug].
class StoreBySlugProvider extends AutoDisposeFutureProvider<StoreModel?> {
  /// Provider for store by slug
  ///
  /// Copied from [storeBySlug].
  StoreBySlugProvider(String slug)
    : this._internal(
        (ref) => storeBySlug(ref as StoreBySlugRef, slug),
        from: storeBySlugProvider,
        name: r'storeBySlugProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$storeBySlugHash,
        dependencies: StoreBySlugFamily._dependencies,
        allTransitiveDependencies: StoreBySlugFamily._allTransitiveDependencies,
        slug: slug,
      );

  StoreBySlugProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.slug,
  }) : super.internal();

  final String slug;

  @override
  Override overrideWith(
    FutureOr<StoreModel?> Function(StoreBySlugRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: StoreBySlugProvider._internal(
        (ref) => create(ref as StoreBySlugRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        slug: slug,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<StoreModel?> createElement() {
    return _StoreBySlugProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is StoreBySlugProvider && other.slug == slug;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, slug.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin StoreBySlugRef on AutoDisposeFutureProviderRef<StoreModel?> {
  /// The parameter `slug` of this provider.
  String get slug;
}

class _StoreBySlugProviderElement
    extends AutoDisposeFutureProviderElement<StoreModel?>
    with StoreBySlugRef {
  _StoreBySlugProviderElement(super.provider);

  @override
  String get slug => (origin as StoreBySlugProvider).slug;
}

String _$storesByCategoryHash() => r'bb237ebcda910c0af3de8d3c3d3df5a53193f054';

/// Provider for stores by category
///
/// Copied from [storesByCategory].
@ProviderFor(storesByCategory)
const storesByCategoryProvider = StoresByCategoryFamily();

/// Provider for stores by category
///
/// Copied from [storesByCategory].
class StoresByCategoryFamily extends Family<AsyncValue<List<StoreModel>>> {
  /// Provider for stores by category
  ///
  /// Copied from [storesByCategory].
  const StoresByCategoryFamily();

  /// Provider for stores by category
  ///
  /// Copied from [storesByCategory].
  StoresByCategoryProvider call(
    int categoryId, {
    int page = 1,
    int limit = 20,
  }) {
    return StoresByCategoryProvider(categoryId, page: page, limit: limit);
  }

  @override
  StoresByCategoryProvider getProviderOverride(
    covariant StoresByCategoryProvider provider,
  ) {
    return call(
      provider.categoryId,
      page: provider.page,
      limit: provider.limit,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'storesByCategoryProvider';
}

/// Provider for stores by category
///
/// Copied from [storesByCategory].
class StoresByCategoryProvider
    extends AutoDisposeFutureProvider<List<StoreModel>> {
  /// Provider for stores by category
  ///
  /// Copied from [storesByCategory].
  StoresByCategoryProvider(int categoryId, {int page = 1, int limit = 20})
    : this._internal(
        (ref) => storesByCategory(
          ref as StoresByCategoryRef,
          categoryId,
          page: page,
          limit: limit,
        ),
        from: storesByCategoryProvider,
        name: r'storesByCategoryProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$storesByCategoryHash,
        dependencies: StoresByCategoryFamily._dependencies,
        allTransitiveDependencies:
            StoresByCategoryFamily._allTransitiveDependencies,
        categoryId: categoryId,
        page: page,
        limit: limit,
      );

  StoresByCategoryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categoryId,
    required this.page,
    required this.limit,
  }) : super.internal();

  final int categoryId;
  final int page;
  final int limit;

  @override
  Override overrideWith(
    FutureOr<List<StoreModel>> Function(StoresByCategoryRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: StoresByCategoryProvider._internal(
        (ref) => create(ref as StoresByCategoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categoryId: categoryId,
        page: page,
        limit: limit,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<StoreModel>> createElement() {
    return _StoresByCategoryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is StoresByCategoryProvider &&
        other.categoryId == categoryId &&
        other.page == page &&
        other.limit == limit;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categoryId.hashCode);
    hash = _SystemHash.combine(hash, page.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin StoresByCategoryRef on AutoDisposeFutureProviderRef<List<StoreModel>> {
  /// The parameter `categoryId` of this provider.
  int get categoryId;

  /// The parameter `page` of this provider.
  int get page;

  /// The parameter `limit` of this provider.
  int get limit;
}

class _StoresByCategoryProviderElement
    extends AutoDisposeFutureProviderElement<List<StoreModel>>
    with StoresByCategoryRef {
  _StoresByCategoryProviderElement(super.provider);

  @override
  int get categoryId => (origin as StoresByCategoryProvider).categoryId;
  @override
  int get page => (origin as StoresByCategoryProvider).page;
  @override
  int get limit => (origin as StoresByCategoryProvider).limit;
}

String _$storeSearchHash() => r'cc3e21626fecaf6d127344156cb2305413356297';

/// Provider for store search
///
/// Copied from [storeSearch].
@ProviderFor(storeSearch)
const storeSearchProvider = StoreSearchFamily();

/// Provider for store search
///
/// Copied from [storeSearch].
class StoreSearchFamily extends Family<AsyncValue<List<StoreModel>>> {
  /// Provider for store search
  ///
  /// Copied from [storeSearch].
  const StoreSearchFamily();

  /// Provider for store search
  ///
  /// Copied from [storeSearch].
  StoreSearchProvider call(String query, {int page = 1, int limit = 20}) {
    return StoreSearchProvider(query, page: page, limit: limit);
  }

  @override
  StoreSearchProvider getProviderOverride(
    covariant StoreSearchProvider provider,
  ) {
    return call(provider.query, page: provider.page, limit: provider.limit);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'storeSearchProvider';
}

/// Provider for store search
///
/// Copied from [storeSearch].
class StoreSearchProvider extends AutoDisposeFutureProvider<List<StoreModel>> {
  /// Provider for store search
  ///
  /// Copied from [storeSearch].
  StoreSearchProvider(String query, {int page = 1, int limit = 20})
    : this._internal(
        (ref) =>
            storeSearch(ref as StoreSearchRef, query, page: page, limit: limit),
        from: storeSearchProvider,
        name: r'storeSearchProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$storeSearchHash,
        dependencies: StoreSearchFamily._dependencies,
        allTransitiveDependencies: StoreSearchFamily._allTransitiveDependencies,
        query: query,
        page: page,
        limit: limit,
      );

  StoreSearchProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
    required this.page,
    required this.limit,
  }) : super.internal();

  final String query;
  final int page;
  final int limit;

  @override
  Override overrideWith(
    FutureOr<List<StoreModel>> Function(StoreSearchRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: StoreSearchProvider._internal(
        (ref) => create(ref as StoreSearchRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
        page: page,
        limit: limit,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<StoreModel>> createElement() {
    return _StoreSearchProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is StoreSearchProvider &&
        other.query == query &&
        other.page == page &&
        other.limit == limit;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);
    hash = _SystemHash.combine(hash, page.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin StoreSearchRef on AutoDisposeFutureProviderRef<List<StoreModel>> {
  /// The parameter `query` of this provider.
  String get query;

  /// The parameter `page` of this provider.
  int get page;

  /// The parameter `limit` of this provider.
  int get limit;
}

class _StoreSearchProviderElement
    extends AutoDisposeFutureProviderElement<List<StoreModel>>
    with StoreSearchRef {
  _StoreSearchProviderElement(super.provider);

  @override
  String get query => (origin as StoreSearchProvider).query;
  @override
  int get page => (origin as StoreSearchProvider).page;
  @override
  int get limit => (origin as StoreSearchProvider).limit;
}

String _$storeSectionsHash() => r'b32852d235e83f1a4d19f829cc7bcf2275a05fb5';

/// Provider for store sections
///
/// Copied from [storeSections].
@ProviderFor(storeSections)
const storeSectionsProvider = StoreSectionsFamily();

/// Provider for store sections
///
/// Copied from [storeSections].
class StoreSectionsFamily extends Family<AsyncValue<List<StoreSectionModel>>> {
  /// Provider for store sections
  ///
  /// Copied from [storeSections].
  const StoreSectionsFamily();

  /// Provider for store sections
  ///
  /// Copied from [storeSections].
  StoreSectionsProvider call(int storeId) {
    return StoreSectionsProvider(storeId);
  }

  @override
  StoreSectionsProvider getProviderOverride(
    covariant StoreSectionsProvider provider,
  ) {
    return call(provider.storeId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'storeSectionsProvider';
}

/// Provider for store sections
///
/// Copied from [storeSections].
class StoreSectionsProvider
    extends AutoDisposeFutureProvider<List<StoreSectionModel>> {
  /// Provider for store sections
  ///
  /// Copied from [storeSections].
  StoreSectionsProvider(int storeId)
    : this._internal(
        (ref) => storeSections(ref as StoreSectionsRef, storeId),
        from: storeSectionsProvider,
        name: r'storeSectionsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$storeSectionsHash,
        dependencies: StoreSectionsFamily._dependencies,
        allTransitiveDependencies:
            StoreSectionsFamily._allTransitiveDependencies,
        storeId: storeId,
      );

  StoreSectionsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.storeId,
  }) : super.internal();

  final int storeId;

  @override
  Override overrideWith(
    FutureOr<List<StoreSectionModel>> Function(StoreSectionsRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: StoreSectionsProvider._internal(
        (ref) => create(ref as StoreSectionsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        storeId: storeId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<StoreSectionModel>> createElement() {
    return _StoreSectionsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is StoreSectionsProvider && other.storeId == storeId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, storeId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin StoreSectionsRef
    on AutoDisposeFutureProviderRef<List<StoreSectionModel>> {
  /// The parameter `storeId` of this provider.
  int get storeId;
}

class _StoreSectionsProviderElement
    extends AutoDisposeFutureProviderElement<List<StoreSectionModel>>
    with StoreSectionsRef {
  _StoreSectionsProviderElement(super.provider);

  @override
  int get storeId => (origin as StoreSectionsProvider).storeId;
}

String _$storeStatsHash() => r'42e51669e645efc70c8e6dcf5fc34bc4e631af81';

/// Provider for store statistics
///
/// Copied from [storeStats].
@ProviderFor(storeStats)
const storeStatsProvider = StoreStatsFamily();

/// Provider for store statistics
///
/// Copied from [storeStats].
class StoreStatsFamily extends Family<AsyncValue<StoreStatsModel>> {
  /// Provider for store statistics
  ///
  /// Copied from [storeStats].
  const StoreStatsFamily();

  /// Provider for store statistics
  ///
  /// Copied from [storeStats].
  StoreStatsProvider call(int storeId) {
    return StoreStatsProvider(storeId);
  }

  @override
  StoreStatsProvider getProviderOverride(
    covariant StoreStatsProvider provider,
  ) {
    return call(provider.storeId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'storeStatsProvider';
}

/// Provider for store statistics
///
/// Copied from [storeStats].
class StoreStatsProvider extends AutoDisposeFutureProvider<StoreStatsModel> {
  /// Provider for store statistics
  ///
  /// Copied from [storeStats].
  StoreStatsProvider(int storeId)
    : this._internal(
        (ref) => storeStats(ref as StoreStatsRef, storeId),
        from: storeStatsProvider,
        name: r'storeStatsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$storeStatsHash,
        dependencies: StoreStatsFamily._dependencies,
        allTransitiveDependencies: StoreStatsFamily._allTransitiveDependencies,
        storeId: storeId,
      );

  StoreStatsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.storeId,
  }) : super.internal();

  final int storeId;

  @override
  Override overrideWith(
    FutureOr<StoreStatsModel> Function(StoreStatsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: StoreStatsProvider._internal(
        (ref) => create(ref as StoreStatsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        storeId: storeId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<StoreStatsModel> createElement() {
    return _StoreStatsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is StoreStatsProvider && other.storeId == storeId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, storeId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin StoreStatsRef on AutoDisposeFutureProviderRef<StoreStatsModel> {
  /// The parameter `storeId` of this provider.
  int get storeId;
}

class _StoreStatsProviderElement
    extends AutoDisposeFutureProviderElement<StoreStatsModel>
    with StoreStatsRef {
  _StoreStatsProviderElement(super.provider);

  @override
  int get storeId => (origin as StoreStatsProvider).storeId;
}

String _$currentStoreHash() => r'42e16b281d3ab8338e58494d1798cf11fa1c73ae';

/// State provider for current store view
///
/// Copied from [CurrentStore].
@ProviderFor(CurrentStore)
final currentStoreProvider =
    AutoDisposeNotifierProvider<CurrentStore, StoreModel?>.internal(
      CurrentStore.new,
      name: r'currentStoreProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$currentStoreHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CurrentStore = AutoDisposeNotifier<StoreModel?>;
String _$storeCategoryFilterHash() =>
    r'2cd2b4dde85f4a3a34c96cedf18ca4f862468a1c';

/// State provider for store categories filter
///
/// Copied from [StoreCategoryFilter].
@ProviderFor(StoreCategoryFilter)
final storeCategoryFilterProvider =
    AutoDisposeNotifierProvider<StoreCategoryFilter, int?>.internal(
      StoreCategoryFilter.new,
      name: r'storeCategoryFilterProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$storeCategoryFilterHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$StoreCategoryFilter = AutoDisposeNotifier<int?>;
String _$storeSearchQueryHash() => r'9d5f42487ef923f575bcddab3e6482843c05c801';

/// State provider for store search query
///
/// Copied from [StoreSearchQuery].
@ProviderFor(StoreSearchQuery)
final storeSearchQueryProvider =
    AutoDisposeNotifierProvider<StoreSearchQuery, String>.internal(
      StoreSearchQuery.new,
      name: r'storeSearchQueryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$storeSearchQueryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$StoreSearchQuery = AutoDisposeNotifier<String>;
String _$storeLoadingStateHash() => r'7e8b9471400997476e9c2cdebf1b4f28a6d39d93';

/// State provider for store loading states
///
/// Copied from [StoreLoadingState].
@ProviderFor(StoreLoadingState)
final storeLoadingStateProvider =
    AutoDisposeNotifierProvider<StoreLoadingState, Map<String, bool>>.internal(
      StoreLoadingState.new,
      name: r'storeLoadingStateProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$storeLoadingStateHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$StoreLoadingState = AutoDisposeNotifier<Map<String, bool>>;
String _$storeOperationsHash() => r'cd44a8be0e7ff9e9a5e77b5abc9e50805d9cc5ae';

/// Provider for store operations (create, update, etc.)
///
/// Copied from [StoreOperations].
@ProviderFor(StoreOperations)
final storeOperationsProvider =
    AutoDisposeAsyncNotifierProvider<StoreOperations, void>.internal(
      StoreOperations.new,
      name: r'storeOperationsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$storeOperationsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$StoreOperations = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
