// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_VehicleMake _$VehicleMakeFromJson(Map<String, dynamic> json) => _VehicleMake(
  id: (json['id'] as num).toInt(),
  name: json['name'] as String,
  country: json['country'] as String?,
  isActive: json['is_active'] as bool? ?? true,
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
  isDeleted: json['is_deleted'] as bool? ?? false,
);

Map<String, dynamic> _$VehicleMakeToJson(_VehicleMake instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'country': instance.country,
      'is_active': instance.isActive,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'is_deleted': instance.isDeleted,
    };
