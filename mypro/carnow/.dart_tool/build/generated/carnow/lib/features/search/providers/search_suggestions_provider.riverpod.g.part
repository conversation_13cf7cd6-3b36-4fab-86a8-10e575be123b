// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$popularSearchesHash() => r'caacc95d5c5748659bbf824e62406f8e0a418153';

/// Provider for popular searches (from API)
///
/// Copied from [popularSearches].
@ProviderFor(popularSearches)
final popularSearchesProvider =
    AutoDisposeFutureProvider<List<String>>.internal(
      popularSearches,
      name: r'popularSearchesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$popularSearchesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PopularSearchesRef = AutoDisposeFutureProviderRef<List<String>>;
String _$searchSuggestionsHash() => r'cced5f86d6914865695246b0e9b359be71d13dba';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$SearchSuggestions
    extends BuildlessAutoDisposeAsyncNotifier<List<String>> {
  late final String query;

  FutureOr<List<String>> build(String query);
}

/// Provider that returns auto-complete suggestions based on the user's
/// search input - FIXED: Uses SimpleApiClient instead of direct Supabase calls
///
/// Copied from [SearchSuggestions].
@ProviderFor(SearchSuggestions)
const searchSuggestionsProvider = SearchSuggestionsFamily();

/// Provider that returns auto-complete suggestions based on the user's
/// search input - FIXED: Uses SimpleApiClient instead of direct Supabase calls
///
/// Copied from [SearchSuggestions].
class SearchSuggestionsFamily extends Family<AsyncValue<List<String>>> {
  /// Provider that returns auto-complete suggestions based on the user's
  /// search input - FIXED: Uses SimpleApiClient instead of direct Supabase calls
  ///
  /// Copied from [SearchSuggestions].
  const SearchSuggestionsFamily();

  /// Provider that returns auto-complete suggestions based on the user's
  /// search input - FIXED: Uses SimpleApiClient instead of direct Supabase calls
  ///
  /// Copied from [SearchSuggestions].
  SearchSuggestionsProvider call(String query) {
    return SearchSuggestionsProvider(query);
  }

  @override
  SearchSuggestionsProvider getProviderOverride(
    covariant SearchSuggestionsProvider provider,
  ) {
    return call(provider.query);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'searchSuggestionsProvider';
}

/// Provider that returns auto-complete suggestions based on the user's
/// search input - FIXED: Uses SimpleApiClient instead of direct Supabase calls
///
/// Copied from [SearchSuggestions].
class SearchSuggestionsProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<SearchSuggestions, List<String>> {
  /// Provider that returns auto-complete suggestions based on the user's
  /// search input - FIXED: Uses SimpleApiClient instead of direct Supabase calls
  ///
  /// Copied from [SearchSuggestions].
  SearchSuggestionsProvider(String query)
    : this._internal(
        () => SearchSuggestions()..query = query,
        from: searchSuggestionsProvider,
        name: r'searchSuggestionsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$searchSuggestionsHash,
        dependencies: SearchSuggestionsFamily._dependencies,
        allTransitiveDependencies:
            SearchSuggestionsFamily._allTransitiveDependencies,
        query: query,
      );

  SearchSuggestionsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final String query;

  @override
  FutureOr<List<String>> runNotifierBuild(
    covariant SearchSuggestions notifier,
  ) {
    return notifier.build(query);
  }

  @override
  Override overrideWith(SearchSuggestions Function() create) {
    return ProviderOverride(
      origin: this,
      override: SearchSuggestionsProvider._internal(
        () => create()..query = query,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<SearchSuggestions, List<String>>
  createElement() {
    return _SearchSuggestionsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SearchSuggestionsProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SearchSuggestionsRef
    on AutoDisposeAsyncNotifierProviderRef<List<String>> {
  /// The parameter `query` of this provider.
  String get query;
}

class _SearchSuggestionsProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<SearchSuggestions, List<String>>
    with SearchSuggestionsRef {
  _SearchSuggestionsProviderElement(super.provider);

  @override
  String get query => (origin as SearchSuggestionsProvider).query;
}

String _$recentSearchesHash() => r'713b4f19da0acfb65b6b49735ec445c5f46edaed';

/// Provider for getting recent searches from local storage
///
/// Copied from [RecentSearches].
@ProviderFor(RecentSearches)
final recentSearchesProvider =
    AutoDisposeAsyncNotifierProvider<RecentSearches, List<String>>.internal(
      RecentSearches.new,
      name: r'recentSearchesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$recentSearchesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$RecentSearches = AutoDisposeAsyncNotifier<List<String>>;
String _$searchTrackerHash() => r'ea18dea4f187e4ee5d4a349e032547e9dea3f336';

/// Helper function to track search analytics
///
/// Copied from [SearchTracker].
@ProviderFor(SearchTracker)
final searchTrackerProvider =
    AutoDisposeNotifierProvider<SearchTracker, void>.internal(
      SearchTracker.new,
      name: r'searchTrackerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$searchTrackerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SearchTracker = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
