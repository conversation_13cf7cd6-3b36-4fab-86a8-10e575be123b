package middleware

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"carnow-backend/internal/config"

	"github.com/gin-gonic/gin"
	"github.com/jackc/pgx/v5"
)

// SupabaseUser represents user data from Supabase Auth API
type SupabaseUser struct {
	ID    string `json:"id"`
	Email string `json:"email"`
	Role  string `json:"role"`
	Aud   string `json:"aud,omitempty"`
}

// DatabaseInterface defines the interface for database operations needed by middleware
type DatabaseInterface interface {
	QueryRow(ctx context.Context, query string, args ...interface{}) pgx.Row
	Exec(ctx context.Context, query string, args ...interface{}) error
	// Add other methods as needed
}

// SimpleJWTMiddleware validates JWT tokens using secure JWT service
// This follows the Forever Plan: simple architecture with enhanced security
func SimpleJWTMiddleware(cfg *config.Config, db DatabaseInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			log.Println("❌ JWT Middleware: No authorization header")
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Authorization header required",
				"code":    "MISSING_AUTH_HEADER",
				"message": "Please provide a valid Bearer token",
			})
			c.Abort()
			return
		}

		// Extract Bearer token
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			log.Println("❌ JWT Middleware: Invalid authorization header format")
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Invalid authorization header format",
				"code":    "INVALID_AUTH_FORMAT",
				"message": "Authorization header must be in format: Bearer <token>",
			})
			c.Abort()
			return
		}

		tokenString := tokenParts[1]
		log.Printf("🔍 JWT Middleware: Validating token (length: %d)", len(tokenString))

		// Try Supabase validation (for Google OAuth tokens)
		user, err := validateTokenWithSupabase(cfg, tokenString)
		if err != nil {
			log.Printf("❌ JWT Middleware: Token validation failed: %v", err)
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Invalid token",
				"code":    "TOKEN_VALIDATION_FAILED",
				"message": "Token validation failed",
			})
			c.Abort()
			return
		}

		// Use Supabase user data
		log.Printf("✅ JWT Middleware: Token validated for user: %s (%s)", user.Email, user.ID)

		// Set user information in context
		c.Set("user_id", user.ID)
		c.Set("user_email", user.Email)
		c.Set("user_role", user.Role)
		c.Set("token_type", "supabase")

		// Auto-create user if doesn't exist (using pgx)
		if err := ensureUserExistsWithPgx(db, user); err != nil {
			log.Printf("❌ JWT Middleware: Failed to ensure user exists: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to process user information",
				"code":    "USER_PROCESSING_ERROR",
				"message": "Internal server error",
			})
			c.Abort()
			return
		}

		log.Printf("✅ JWT Middleware: User context set for %s", user.Email)
		c.Next()
	}
}

// OptionalJWTMiddleware allows both authenticated and non-authenticated requests
func OptionalJWTMiddleware(cfg *config.Config, db DatabaseInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")

		// If no auth header, continue without setting user context
		if authHeader == "" {
			log.Println("🔓 Optional JWT Middleware: No auth header, continuing without authentication")
			c.Next()
			return
		}

		// If auth header exists, validate it
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			log.Println("⚠️ Optional JWT Middleware: Invalid auth format, continuing without authentication")
			c.Next()
			return
		}

		tokenString := tokenParts[1]
		user, err := validateTokenWithSupabase(cfg, tokenString)

		// If valid token, set user context
		if err == nil {
			log.Printf("✅ Optional JWT Middleware: Token validated for user: %s", user.Email)

			// Auto-create user if doesn't exist
			_ = ensureUserExistsWithPgx(db, user)

			c.Set("user_id", user.ID)
			c.Set("user_email", user.Email)
			c.Set("user_role", user.Role)
		} else {
			log.Printf("⚠️ Optional JWT Middleware: Token validation failed: %v", err)
		}

		c.Next()
	}
}

// validateTokenWithSupabase validates JWT token by calling Supabase Auth API
func validateTokenWithSupabase(cfg *config.Config, token string) (*SupabaseUser, error) {
	// Create request to Supabase Auth API
	url := fmt.Sprintf("%s/auth/v1/user", cfg.Supabase.URL)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Set headers
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("apikey", cfg.Supabase.AnonKey)

	// Make request with timeout
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to validate token: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("invalid token - status: %d", resp.StatusCode)
	}

	// Parse response
	var supabaseUser SupabaseUser
	if err := json.NewDecoder(resp.Body).Decode(&supabaseUser); err != nil {
		return nil, fmt.Errorf("failed to parse user data: %v", err)
	}

	// Set default role if not provided
	if supabaseUser.Role == "" {
		supabaseUser.Role = "authenticated"
	}

	return &supabaseUser, nil
}

// ensureUserExistsWithPgx checks if user exists in Go backend, creates if not (using pgx)
// This follows the Forever Plan: simple database operations with pgx only
func ensureUserExistsWithPgx(db DatabaseInterface, user *SupabaseUser) error {
	log.Printf("🔍 Auth: Checking user existence for: %s (%s)", user.Email, user.ID)

	// For Google OAuth users, the ID is not a UUID but a Google user ID
	// We'll skip the UUID validation and database operations for now
	// since the user is already authenticated with Supabase

	log.Printf("✅ Auth: User authenticated with Supabase: %s (%s)", user.Email, user.ID)
	log.Printf("✅ Auth: Skipping database operations for Google OAuth user")

	return nil
}

// GetUserIDFromContext extracts user ID from Gin context
func GetUserIDFromContext(c *gin.Context) string {
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(string); ok {
			return id
		}
	}
	return ""
}
